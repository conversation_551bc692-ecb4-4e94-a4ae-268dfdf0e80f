<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Create Project Tracker Manager Group -->
        <record id="group_project_tracker_manager" model="res.groups">
            <field name="name">Project Tracker Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_project_tracker_designer" model="res.groups">
            <field name="name">Designer</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_project_tracker_pd_hod" model="res.groups">
            <field name="name">PD-HOD</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_activity_cancel_rights" model="res.groups">
            <field name="name">Activity Cancel Rights</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_create_revision" model="res.groups">
            <field name="name">Can Create Revisions</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_boughtout_manager" model="res.groups">
            <field name="name">Boughtout Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_md" model="res.groups">
            <field name="name">MD</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_coo" model="res.groups">
            <field name="name">COO</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_pmo" model="res.groups">
            <field name="name">PMO</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_finance" model="res.groups">
            <field name="name">Finance</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_accounts" model="res.groups">
            <field name="name">Accounts</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>

        <record id="group_proform_scm" model="res.groups">
            <field name="name">SCM</field>
            <field name="category_id" ref="base.module_category_hidden"/>
        </record>
    </data>
</odoo>