from odoo import models, fields, api

class StageSelectionWizard(models.TransientModel):
    _name = 'stage.selection.wizard'
    _description = 'Stage Selection Wizard'

    tracker_id = fields.Many2one('project.tracker', string="Project", required=True)

    fixed_stage_ids = fields.Many2many(
        'project.stage',
        'stage_wizard_fixed_rel',
        'wizard_id',
        'stage_id',
        string="Fixed Stages (Always Included)",
        default=lambda self: self._get_fixed_stage_ids(),
        store=True,
    )

    stage_ids = fields.Many2many(
        'project.stage',
        'stage_wizard_selectable_rel',
        'wizard_id',
        'stage_id',
        string="Select Additional Allowed Stages",
        domain=lambda self: self._get_stage_domain()
    )

    def _get_fixed_stage_ids(self):
        fixed_ids = self.env['project.stage'].search([
            ('stage_selection', 'in', [
                'design', 'prod_dwngs', 'production_eng', 'assembly', 'quality_check', 'packaging', 'ready_for_dispatch', 'dispatch', 'installation','hold'
            ])
        ])
        return fixed_ids

    @api.model
    def _get_stage_domain(self):
        all_stage_ids = self.env['project.stage'].search([], order='id').ids
        fixed_ids = self._get_fixed_stage_ids().ids
        available_ids = list(set(all_stage_ids) - set(fixed_ids))
        return [('id', 'in', available_ids)]

    def action_confirm_stages(self):
        fixed_stage_ids = self._get_fixed_stage_ids().ids
        selected_stage_ids = self.stage_ids.ids
        all_ids = list(set(fixed_stage_ids + selected_stage_ids))
        self.tracker_id.available_stage_ids = [(6, 0, all_ids)]
        return {'type': 'ir.actions.act_window_close'}
