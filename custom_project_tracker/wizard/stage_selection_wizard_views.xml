<odoo>
    <data>
        <record id="view_stage_selection_wizard_form" model="ir.ui.view">
            <field name="name">stage.selection.wizard.form</field>
            <field name="model">stage.selection.wizard</field>
            <field name="arch" type="xml">
                <form string="Select Allowed Stages">
                    <group>
                        <field name="stage_ids" widget="many2many_tags"/>
                        <field name="fixed_stage_ids" widget="many2many_tags" readonly="1"/>
                    </group>
                    <footer>
                        <button name="action_confirm_stages" type="object" string="Confirm" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <record id="action_stage_selection_wizard" model="ir.actions.act_window">
            <field name="name">Stage Selection Wizard</field>
            <field name="res_model">stage.selection.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>