from odoo import models, fields, api
import re

class ProjectTrackerRevisionWizard(models.TransientModel):
    _name = 'project.tracker.revision.wizard'
    _description = 'Project Tracker Revision Wizard'

    project_tracker_id = fields.Many2one('project.tracker', string="Project", required=True)
    new_stage_id = fields.Many2one('project.stage', string="Move to Stage", domain="[('stage_selection', 'in', ['design', 'prod_dwngs'])]")
    available_stage_ids = fields.Many2many('project.stage',)
    design_end_date = fields.Date("Design End Date",)
    reason = fields.Char(string="Reason for Revision", required=True)

    customer_id = fields.Many2one('res.partner', string="Client Name")
    quotation_id = fields.Many2one('sale.order', string="Quotation")
    item_name = fields.Char(string="Item Name")
    qty = fields.Float(string="Quantity")
    planned_dispatch = fields.Date(string="Planned Dispatch")
    item_no = fields.Char(string="Item No")

    def _get_move_stage_ids(self):
        fixed_ids = self.env['project.stage'].search([
            ('stage_selection', 'in', [
                'design', 'prod_dwngs'
            ])
        ])
        return fixed_ids

    def action_confirm(self):
        self.ensure_one()
        original = self.project_tracker_id

        original.stage_transition_ids.unlink()
        
        # Extract base item and current revision number (if any)
        match = re.match(r'^(.*)-REV-(\d+)$', original.item_no)
        if match:
            base_item = match.group(1)
            last_rev_number = int(match.group(2))
        else:
            base_item = original.item_no
            last_rev_number = 0

        # Compute new revision number
        new_rev_number = last_rev_number + 1
        new_item_no = f"{base_item}-REV-{new_rev_number}"

        allowed_stage_ids = self.env['project.stage'].search([
            ('stage_selection', 'in', ['design', 'prod_dwngs', 'production_eng', 'assembly', 'quality_check', 'packaging', 'ready_for_dispatch', 'dispatch', 'installation'])
        ]).ids
        # Duplicate the record
        new_record = original.copy({
            'item_no': new_item_no,
            'design_end_date':self.design_end_date,
            'stage_id': self.new_stage_id.id,
            'prod_dwngs_end_date': False,
            'production_eng_end_date': False,
            'carpentry_end_date': False,
            'metal_shop_end_date': False,
            'stone_end_date': False,
            'polishing_end_date': False,
            'upholstry_end_date': False,
            'assembly_end_date': False,
            'packaging_end_date': False,
            'dispatch_end_date': False,
            'installation_end_date': False,
            'parent_tracker_id': original.id,
            'is_revision': True,
            'hold': False,
            'un_hold': False,
            'revision_needed': False,
            'hold_requested': False,
            'revision_done': False,
            'available_stage_ids': [(6, 0, allowed_stage_ids)],
        })
        original.active = False
        original.work_status = 'revision'
        revision_obj = self.env['project.tracker.revision']
        

        for rec in original.revision_ids:
            self.env['project.tracker.revision'].create({
                'tracker_id': new_record.id,
                'name': rec.name,
                'date': rec.date,
                'customer_id': rec.customer_id.id if rec.customer_id else False,
                'quotation_id': rec.quotation_id.id if rec.quotation_id else False,
                'item_name': rec.item_name,
                'item_no': rec.item_no,
                'qty': rec.qty,
                'planned_dispatch': rec.planned_dispatch,
            })

        revision_obj.create({
            'tracker_id': new_record.id,
            'name': self.reason,
            'date': fields.Date.context_today(self),
            'customer_id': self.customer_id.id if self.customer_id else False,
            'quotation_id': self.quotation_id.id if self.quotation_id else False,
            'item_name': self.item_name,
            'item_no': self.item_no,
            'qty': self.qty,
            'planned_dispatch': self.planned_dispatch,
        })
        
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'project.tracker',
            'view_mode': 'form',
            'res_id': new_record.id,
            'target': 'current',
        }
