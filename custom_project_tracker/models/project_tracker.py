from odoo import models, fields, api, _
from odoo.exceptions import ValidationError, UserError
from markupsafe import Markup
from datetime import date
from dateutil.relativedelta import relativedelta
from lxml import etree
import json


class ProjectTracker(models.Model):
    _name = 'project.tracker'
    _description = 'Project Tracker'
    _rec_name = 'item_no'
    _inherit = ['mail.thread.cc',
                'mail.tracking.duration.mixin',
                'mail.thread',
                'mail.activity.mixin',
               ]
    _track_duration_field = 'stage_id'



    project_count = fields.Integer(string='Number of Projects', default="1")
    customer_id = fields.Many2one('res.partner', string="Customer Name", required=True, tracking=True)
    prod_rec_date = fields.Date("Production Received Date", tracking=True)
    planned_dispatch = fields.Date(string="Planned Dispatch", required=False, tracking=True)
    designer_id = fields.Many2one('res.users', string="Designer", tracking=True, domain="[('is_designer', '=', True)]")
    drgs_planned_eta = fields.Date("Drgs Planned ETA", tracking=True)
    drgs_recd_dt = fields.Date("Drgs Recd Dt", tracking=True)
    work_category = fields.Selection([
        ('millwork', 'Millwork'),
        ('furniture', 'Furniture'),
        ('mw_fur', 'MW + Fur'),
    ], string="Work Category")
    item_no = fields.Char("Item No.", tracking=True, required=True)
    item_name = fields.Char("Item Name", tracking=True, required=True)
    finish_material = fields.Char("Item Finish", tracking=True)
    finish_material_qty = fields.Float("Qty", required=True, tracking=True)
    item_no_detail = fields.Selection([
        ('elevations', 'Elevations'),
        ('parts', 'Parts'),
        ('additions', 'Additions'),
        ('combined_with', 'Combined with'),
        ('site_return', 'Site Return'),
    ], string="Item No. Detail")
    contractor_id = fields.Many2one('res.partner', string="Contractor", tracking=True, 
                                   domain="[('contact_type', '=', 'contractor')]")
    stage_id = fields.Many2one('project.stage', string="Status", tracking=True, default=lambda self: self._default_stage_id(), domain="[('id', 'in', available_stage_ids)]")
    date = fields.Date("Quote Date", tracking=True)
    remarks = fields.Text("Remarks")
    drawing_image_ids = fields.Many2many('ir.attachment', relation="m2m_ir_attachment_relation", column1="m2m_id", column2="attachment_id", string='Drawing Images')
    area_layout = fields.Char(string="Layout Area", tracking=True)
    related_item_name = fields.Many2one('project.tracker', string="Related Item Name",
                                        domain="[('customer_id', '=', customer_id), ('sale_order_id', '=', sale_order_id), ('id', '!=', id)]"
)
    active = fields.Boolean(string='Active', default=True)
    parent_tracker_id = fields.Many2one('project.tracker', string="Parent Project Tracker")
    is_revision = fields.Boolean(string="Is Revision", default=False)

    available_stage_ids = fields.Many2many(
        'project.stage',
        string="Allowed Stages",
        default=lambda self: self._default_available_stages(),
        help="Only these stages will be selectable in the 'Status' field.",
    )

    ppt_update = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="PPT Update")

    quote_update = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Quote Update")

    drg_download = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Drg Download")
    
    download_date = fields.Date(string="Download Dt")
    
    
    ppt_final = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="PPT Final")

    measurement_reqd = fields.Selection([
        ('yes', 'Yes'),
        ('no', 'No')
    ], string="Measurement Reqd")

    measurement_recd = fields.Date(string="Measurement Recd")
    
    msmt_recheck = fields.Selection([
    ('yes', 'Yes'),
    ('not_needed', 'Not needed'),
    ('to_be_done', 'To be done')
    ], string="Msmt Recheck")
    
    engg_drgs = fields.Selection([
        ('open', 'Open'),
        ('wip', 'WIP'),
        ('done', 'Done'),
    ], string='Engg Drgs')
    
    bom_jc = fields.Selection([
        ('done', 'Done'),
        ('open', 'Open'),
    ], string='BOM/ JC')

    cutting_list = fields.Selection([
        ('done', 'Done'),
        ('open', 'Open'),
    ], string='Cutting List')
    
    qc_status = fields.Selection([
        ('ok', 'Ok'),
        ('not_ok', 'Not ok'),
    ], string='QC Status')
    
    final_qc_status = fields.Selection([
        ('ok', 'Ok'),
        ('not_ok', 'Not ok'),
    ], string='Final QC Status')
    task_assign_date = fields.Date(string="Task assigned date")
    dispatch_by = fields.Date(string="Dispatch By")
    vehicle_no = fields.Char(string="Vehicle No")
    dispatch_date = fields.Datetime(string="Date of Dispatch")
    site_arrival_date = fields.Date(string="Site Arrival Date")
    assigned_to = fields.Many2one('res.users', string="Assigned To (Supervisor)")
    return_to_factory = fields.Boolean(string="Return to Factory")
    return_remarks = fields.Text(string="Remarks")

    site_qc = fields.Selection([
        ('open', 'Open'),
        ('approved', 'Approved'),
        ('needs_rectification', 'Needs rectification'),
    ], string="Site QC")
#### <<<<<<<============== Hold Project Information================>>>>>>>>>>>>>>

    hold = fields.Boolean(string='Hold', default=False, tracking=True)
    hold_start_date = fields.Datetime("Hold Start Date", tracking=True, readonly=True)
    hold_end_date = fields.Datetime("Hold End Date", tracking=True, readonly=True)
    total_hold_duration = fields.Float("Total Hold Duration (Days)", compute="_compute_total_hold_duration", store=True, readonly=True)
    hold_user_ids = fields.Many2many('res.users', string='Hold Notify Users', tracking=True)
    designer = fields.Selection([('design_change','Design Change'),
                                 ('finish_change','Finish Change'),
                                 ('quote_change','Quote Change'),
                                 ('by_client','By Client')],tracking=True, string="Designer")
    pd_hod = fields.Selection([('production_issue','Production Issue'),
                               ('installation_issue','Installation Issue'),
                               ('drawings_issue','Drawings Issue'),
                               ('by_GC','By (GC)')],tracking=True, string="PD-HOD")
    revision_needed = fields.Boolean(string="Revision Needed", default=False)
    no_revision_needed = fields.Boolean(string="No Revision Needed", default=False)
    hold_remarks = fields.Char(string="Remarks", tracking=True,required=True)
    unhold_remarks = fields.Char(string="Remarks", tracking=True,required=True)
    un_hold = fields.Boolean(string="Un Hold", default=False, tracking=True)
    revision_done = fields.Boolean(string="Revision Done", default=False, tracking=True)
    hold_requested = fields.Boolean(string="Hold Requested", default=False)

    def action_hold(self):
        self.ensure_one()
        self.hold_requested = True

        hold_stage = self.env['project.stage'].search([('stage_selection', '=', 'hold')], limit=1)
        self.with_context(bypass_activity=True).write({'stage_id': hold_stage.id})

        users = self.env['res.users'].search([])
        model_id = self.env['ir.model']._get_id('project.tracker')
        activity_type = self.env.ref('mail.mail_activity_data_todo')

        for user in users:
            self.env['mail.activity'].create({
                'res_id': self.id,
                'res_model_id': model_id,
                'is_hold_activity': True,
                'activity_type_id': activity_type.id,
                'summary': 'Task is taken to Hold status',
                'user_id': user.id,
            })
        group = self.env.ref('custom_project_tracker.group_project_tracker_manager')
        partner_ids = group.users.mapped('partner_id').ids

        message_body = Markup(
            "<p><b>Hold Request Approved</b></p> <p>The item has been placed on <b>Hold</b></p>"
        )

        self.message_post(
            body=message_body,
            subject="Hold Request Approved",
            message_type='notification',
            subtype_xmlid='mail.mt_note',
            partner_ids=partner_ids,
        )

    def action_approve_hold_item(self):
        self.ensure_one()
        self.hold_requested = False

        pending_activities = self.env['mail.activity'].search([
            ('res_model', '=', self._name),
            ('res_id', '=', self.id),
        ])

        for activity in pending_activities:
            activity.action_feedback(feedback='Auto-cancelled due to Hold Approval')

    def action_reject_hold_item(self):
        self.ensure_one()
        self.hold_requested = False

        pending_activities = self.env['mail.activity'].search([
            ('res_model', '=', self._name),
            ('res_id', '=', self.id),
        ])

        for activity in pending_activities:
            activity.action_feedback(feedback='Auto-cancelled due to Hold Approval')

        users = self.env['res.users'].search([])
        model_id = self.env['ir.model']._get_id('project.tracker')
        activity_type = self.env.ref('mail.mail_activity_data_todo')

        for user in users:
            self.env['mail.activity'].create({
                'res_id': self.id,
                'res_model_id': model_id,
                'is_hold_activity': True,
                'activity_type_id': activity_type.id,
                'summary': 'Please Proceed to the Unhold process',
                'user_id': user.id,
            })
#### <<<<<<<============== End Date Information================>>>>>>>>>>>>>>

    design_end_date = fields.Date("Design End Date", required=True, tracking=True)
    prod_dwngs_end_date = fields.Date("Dwngs Planned ETA", tracking=True)
    production_eng_end_date = fields.Date("Production Eng End Date", tracking=True)
    carpentry_end_date = fields.Date("Carpentry End Date", tracking=True)
    metal_shop_end_date = fields.Date("Metal Shop End Date", tracking=True)
    stone_end_date = fields.Date("Stone End Date", tracking=True)
    polishing_end_date = fields.Date("Polishing End Date", tracking=True)
    upholstry_end_date = fields.Date("Upholstry End Date", tracking=True)
    assembly_end_date = fields.Date("Assembly End Date", tracking=True)
    packaging_end_date = fields.Date("Packaging End Date", tracking=True)
    dispatch_end_date = fields.Date("Dispatch End Date", tracking=True)
    installation_end_date = fields.Date("Installation End Date", tracking=True)

#### <<<<<<<============== Boughtout Information================>>>>>>>>>>>>>>

    is_boughtout_req = fields.Boolean(string='Is Boughtout Required')
    boughtout_ids = fields.One2many('boughtout.boughtout', 'project_tracket_id', string='Boughtout')
    is_boughtout_activity = fields.Boolean(string="Is Boughtout Activity", default=False)
    is_pr_created = fields.Boolean(string="Is PR Created", default=False)

#### <<<<<<<============== Revision Information================>>>>>>>>>>>>>>

    revision_ids = fields.One2many('project.tracker.revision', 'tracker_id', string='Revisions')
    has_revisions = fields.Boolean(string="Has Revisions", compute="_compute_has_revisions")

    stage_transition_ids = fields.One2many('project.tracker.stage.transition', 'project_tracker_id', string="Stage Transitions")
    user_id = fields.Many2one('res.users', string='Responsible User', tracking=True)
    
    sale_order_id = fields.Many2one('sale.order', string='Quote No', tracking=True, required=True)
    stage_selection = fields.Selection(related="stage_id.stage_selection", string="Stage Selection", store=True)
    work_status = fields.Selection([
        ('done', 'Done'),
        ('revision', 'Revision'),
        ('delay', 'Delay or Late'),
        ('wip', 'WIP'),
        ('cancelled', 'Cancelled'),
    ], string="Status")

    STAGE_FIELD_MAP = {
        'Prod Dwngs': ('prod_dwngs_end_date'),
        'Production Eng': ('production_eng_end_date'),
        'Carpentry': ('carpentry_end_date'),
        'Metal Shop': ('metal_shop_end_date'),
        'Stone': ('stone_end_date'),
        'Polishing': ('polishing_end_date'),
        'Upholstry': ('upholstry_end_date'),
        'Assembly': ('assembly_end_date'),
        'Packaging': ('packaging_end_date'),
        'Dispatch': ('dispatch_end_date'),
        'Installation': ('installation_end_date'),
    }

    # Add these computed fields
    is_design_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_prod_dwngs_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_production_eng_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_carpentry_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_metal_shop_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_stone_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_polishing_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_upholstry_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_assembly_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_packaging_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_dispatch_stage_available = fields.Boolean(compute='_compute_available_stages')
    is_installation_stage_available = fields.Boolean(compute='_compute_available_stages')

    @api.depends('available_stage_ids')
    def _compute_available_stages(self):
        for record in self:
            # Get all stage selections from available stages
            available_selections = record.available_stage_ids.mapped('stage_selection')
            
            # Set boolean flags based on available selections
            record.is_design_stage_available = 'design' in available_selections
            record.is_prod_dwngs_stage_available = 'prod_dwngs' in available_selections
            record.is_production_eng_stage_available = 'production_eng' in available_selections
            record.is_carpentry_stage_available = 'carpentry' in available_selections
            record.is_metal_shop_stage_available = 'metal_shop' in available_selections
            record.is_stone_stage_available = 'stone' in available_selections
            record.is_polishing_stage_available = 'polishing' in available_selections
            record.is_upholstry_stage_available = 'upholstry' in available_selections
            record.is_assembly_stage_available = 'assembly' in available_selections
            record.is_packaging_stage_available = 'packaging' in available_selections
            record.is_dispatch_stage_available = 'dispatch' in available_selections
            record.is_installation_stage_available = 'installation' in available_selections

    def _default_available_stages(self):
        return self.env['project.stage'].search([('stage_selection', 'in', ['design', 'prod_dwngs', 'production_eng', 'assembly', 'quality_check', 'packaging', 'ready_for_dispatch', 'dispatch', 'installation','hold'])])

    @api.model
    def _default_stage_id(self):
        return self.env['project.stage'].search([], order="sequence asc", limit=1)

    @api.depends('revision_ids')
    def _compute_has_revisions(self):
        for rec in self:
            rec.has_revisions = bool(rec.revision_ids)

    @api.onchange('related_item_name')
    def _onchange_parent_item_id(self):
        if self.related_item_name.stage_id:
            self.stage_id = self.related_item_name.stage_id

    def action_open_stage_wizard(self):
        return {
            'name': 'Select Allowed Stages',
            'type': 'ir.actions.act_window',
            'res_model': 'stage.selection.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_tracker_id': self.id,
            }
        }

    def action_open_project(self):
        for rec in self:
            project = self.env['project.project'].search([('name', '=', rec.sale_order_id.name)], limit=1)
            if project:
                return {
                    'type': 'ir.actions.act_window',
                    'res_model': 'project.project',
                    'view_mode': 'form',
                    'view_id': self.env.ref('project.edit_project').id,
                    'res_id': project.id,
                    'target': 'current',
                }
            else:
                raise UserError("No project found for the given sale order.")


    @api.depends('hold_start_date', 'hold_end_date')
    def _compute_total_hold_duration(self):
        for record in self:
            if record.hold_start_date and record.hold_end_date:
                delta = record.hold_end_date - record.hold_start_date
                record.total_hold_duration = delta.days
            else:
                record.total_hold_duration = 0.0

    def _handle_hold_state_change(self, vals):
        """Handles logic for hold or resume with chatter and manual activity creation"""
        for record in self:
            if vals.get('hold') and not record.hold_start_date:
                record.hold_start_date = fields.Datetime.now()

                # Use all selected users or fallback to first contact user or current user
                assigned_users = record.hold_user_ids or (record.customer_id.user_ids and record.customer_id.user_ids) or self.env.user

                for user in assigned_users:
                    self.env['mail.activity'].create({
                        'res_model_id': self.env.ref('custom_project_tracker.model_project_tracker').id,
                        'res_id': record.id,
                        'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                        'summary': 'Project on Hold',
                        'note': f"The project '{record.item_name}' has been put on hold. Please review.\n\n"
                                f"Item No: {record.item_no}\n"
                                f"Quotation No: {record.sale_order_id.name}",
                        'user_id': user.id,
                    })

            elif not vals.get('hold') and record.hold_start_date and not record.hold_end_date:
                # Project resumed
                record.hold_end_date = fields.Datetime.now()

                record.message_post(
                    body="Project has been resumed from Hold.",
                    message_type="notification"
                )

    @api.model
    def check_eta_and_create_activity(self):
        today = date.today()
        records = self.search([
            ('planned_dispatch', '<', today),
            ('user_id', '!=', False)
        ])
        activity_type = self.env.ref('mail.mail_activity_data_todo')
        for record in records:
            # Check if activity already exists to avoid duplication
            existing = self.env['mail.activity'].search([
                ('res_model', '=', 'project.tracker'),
                ('res_id', '=', record.id),
                ('activity_type_id', '=', activity_type.id),
                ('user_id', '=', record.user_id.id),
                ('summary', '=', 'ETA Missed')
            ])
            if not existing:
                self.env['mail.activity'].create({
                    'activity_type_id': activity_type.id,
                    'res_id': record.id,
                    'res_model_id': self.env['ir.model']._get_id('project.tracker'),
                    'summary': 'ETA Missed',
                    'note': f"The ETA date ({record.planned_dispatch}) for project '{record.display_name}' has been missed.",
                    'user_id': record.user_id.id,
                    'date_deadline': today,
                })

    @api.model
    def create(self, vals):
        first_stage = self.env['project.stage'].search([], order='sequence asc', limit=1)
        if first_stage:
            vals['stage_id'] = first_stage.id
        record = super().create(vals)
        if vals.get('hold'):
                message = Markup(
                    "<span style='color:#DC3545;'>Item is on hold.</span>"
                )
                record.message_post(body=message)
                
        if vals.get('stage_id'):
            stage = self.env['project.stage'].browse(vals['stage_id'])
            today = fields.Date.today()

            record.stage_transition_ids.create({
                'project_tracker_id': record.id,
                'from_stage_id': stage.id,
                'work_start_date': today,
                'stage_deadline_date': vals['design_end_date'],
            })
        return record

    def action_open_stage_transitions_gantt(self):
        self.ensure_one()
        return {
            'name': 'Stage Transitions Gantt',
            'type': 'ir.actions.act_window',
            'res_model': 'project.tracker.stage.transition',
            'view_mode': 'gantt,list,form',
            'domain': [('sale_order_id', '=', self.sale_order_id.id)],
            'context': {
                'default_sale_order_id': self.sale_order_id.id,
                'group_by': ['sale_order_id', 'project_tracker_id']
            },
            'target': 'current',
        }

    def _create_stage_activity(self):
        """Create activity based on stage configuration"""
        self.ensure_one()
        stage = self.stage_id
        
        if stage.required_activity and stage.user_id:
            # Calculate the deadline date
            deadline_date = fields.Date.context_today(self) + relativedelta(days=stage.activity_deadline_days)
            
            # Create the activity
            activity = self.env['mail.activity'].create({
                'res_model_id': self.env['ir.model']._get_id('project.tracker'),
                'res_id': self.id,
                'user_id': stage.user_id.id,
                'summary': stage.activity_summary or f"Required activity for {stage.name}",
                'note': stage.activity_note or f"This activity must be completed before moving to the next stage.",
                'date_deadline': deadline_date,
                'activity_type_id': stage.activity_type_id.id or self.env.ref('mail.mail_activity_data_todo').id,
            })
            
            self.message_post(
                body=f"Activity automatically assigned to {stage.user_id.name} as per stage configuration.",
                message_type="notification"
            )
            
            return activity
        return False

    def _has_pending_required_activities(self):
        """Check if there are pending required activities (boughtout or normal) that block stage transition."""

        self.ensure_one()
        today = fields.Date.context_today(self)

        # Get all activities related to this tracker
        all_activities = self.env['mail.activity'].search([
            ('res_model', '=', 'project.tracker'),
            ('res_id', '=', self.id or self._origin.id),
            ('date_deadline', '>=', today)
        ])

        target_stage = self.stage_id
        target_selection = target_stage.stage_selection if target_stage else ''

        # Separate boughtout and normal activities
        boughtout_activities = all_activities.filtered(lambda a: a.is_boughtout_activity)
        pr_activities = all_activities.filtered(lambda a: a.is_pr_activity)
        normal_activities = all_activities - (boughtout_activities + pr_activities)

        # Block if moving beyond production_eng while boughtout activities are pending
        if boughtout_activities and target_selection not in ['design', 'prod_dwngs', 'production_eng']:
            raise ValidationError(
                "You must complete all Boughtout Activities before proceeding beyond the Production Engineering stage."
            )

        # Block if current stage requires activity and normal activities are still pending
        if normal_activities and self._origin.stage_id.required_activity:
            return True
        
        return False


    @api.onchange('stage_id')
    def _onchange_stage_id(self):
        if self._origin.stage_id.stage_selection != 'hold':
            if self._has_pending_required_activities():
                raise ValidationError(
                    "You must complete the required activity before proceeding to the next stage."
                )
        if self.stage_id:
            new_stage = self.stage_id
            old_stage = self._origin.stage_id
            self.user_id = self.stage_id.user_id

        # Check if moving to a new stage
        if old_stage and old_stage != new_stage:
            today = fields.Date.today()
            old_stage_name = old_stage.name
            
            # Get the end date field for old stage
            end_date_field = self.STAGE_FIELD_MAP.get(old_stage_name)
            if end_date_field:
                planned_end_date = getattr(self, end_date_field, False)
                if planned_end_date and today > planned_end_date:
                    # Set work_status to 'delay' if completion is after planned end date
                    self._origin.work_status = 'delay'

        self._validate_stage_sequence_change(self.stage_id)


    def _update_future_stage_deadlines(self):
        """Update future stage deadlines if current stage completion is delayed"""
        if not self.stage_id:
            return
        
        current_stage = self.stage_id
        current_stage_name = current_stage.name
        
        today = fields.Date.today()
        
        # Get the end date field for current stage
        end_date_field = self.STAGE_FIELD_MAP.get(current_stage_name)
        if not end_date_field:
            return
        
        # Get the planned end date for current stage
        planned_end_date = getattr(self, end_date_field, False)
        if not planned_end_date:
            return
        
        # Calculate delay in days
        delay_days = 0
        if today > planned_end_date:
            delay_days = (today - planned_end_date).days
        
        if delay_days <= 0:
            return
        

        # Only update future stages that are also in available_stage_ids
        future_stages = self.available_stage_ids.filtered(
            lambda s: s.sequence > current_stage.sequence
        ).sorted(key=lambda s: s.sequence)

        # Update deadlines for filtered future stages
        for stage in future_stages:
            stage_name = stage.name
            future_end_date_field = self.STAGE_FIELD_MAP.get(stage_name)

            if future_end_date_field and hasattr(self, future_end_date_field):
                current_deadline = getattr(self, future_end_date_field)
                if current_deadline:
                    # Update the deadline by adding the delay days
                    new_deadline = current_deadline + relativedelta(days=delay_days)
                    setattr(self, future_end_date_field, new_deadline)


    def _validate_stage_sequence_change(self, new_stage):
        if new_stage.stage_selection == 'hold' :
            return

        # if self.hold and new_stage:
        #     raise ValidationError("You cannot move this record to another stage while it's on hold.")

        new_stage_seq = new_stage.sequence

        #Stage 2 or 3 direct checks
        if new_stage_seq in [2, 3]:
            for required_stage_seq in [2, 3]:
                stage_name = next(
                    (s.name for s in self.available_stage_ids if s.sequence == required_stage_seq),
                    None
                )
                if stage_name:
                    date_field = self.STAGE_FIELD_MAP.get(stage_name)
                    if date_field and not getattr(self, date_field):
                        raise ValidationError(
                            f"You cannot move to stage '{new_stage.name}' because required end date "
                            f"for stage '{stage_name}' ('{date_field.replace('_', ' ').title()}') is not filled."
                        )
        elif new_stage_seq > 3:
            for stage in self.available_stage_ids:
                if stage.sequence > 3:
                    date_field = self.STAGE_FIELD_MAP.get(stage.name)
                    if date_field and not getattr(self, date_field):
                        raise ValidationError(
                            f"You cannot move to stage '{new_stage.name}' because allowed stage "
                            f"'{stage.name}' requires end date '{date_field.replace('_', ' ').title()}', "
                            f"but it is not filled."
                        )


    def write(self, vals):
        old_stage = ""
        old_work_status = ""
        if 'stage_id' in vals:
            for record in self:
                old_stage = record.stage_id
                old_work_status = 'done' if record.work_status == 'wip' else record.work_status
                new_stage = self.env['project.stage'].browse(vals['stage_id'])
                record._update_future_stage_deadlines()
                if new_stage.stage_selection != 'hold':
                    if record._has_pending_required_activities():
                        raise ValidationError(
                            "You must complete the required activity before proceeding to the next stage."
                        )
                record._validate_stage_sequence_change(new_stage)

        res = super(ProjectTracker, self).write(vals)
        now = fields.Datetime.now()
        
        for rec in self:
            # Create activity if moved to a new stage that requires it
            if 'stage_id' in vals and (not old_stage or old_stage.id != vals['stage_id']):
                rec._create_stage_activity()
                
            if 'hold' in vals:
                rec._handle_hold_state_change(vals)

            if 'stage_id' in vals:
                rec.work_status = 'wip'
                
            if 'stage_id' in vals and old_stage and old_stage != rec.stage_id:
                now = fields.Datetime.now()
                today = now.date()

                new_stage = rec.stage_id
                old_stage_name = old_stage.name 
                end_date_field = rec.STAGE_FIELD_MAP.get(old_stage_name)
                deadline = getattr(rec, end_date_field, None) if end_date_field else None
                deadline_from_vals = vals.get(end_date_field) if end_date_field else None
                final_deadline = deadline or deadline_from_vals
                if old_stage_name == "Design" and rec.design_end_date:
                    final_deadline = rec.design_end_date

                last_stage_line = rec.stage_transition_ids.filtered(lambda l: not l.to_stage_id).sorted('id', reverse=True)[:1]
                if last_stage_line:
                    wip_days_diff = 0
                    if last_stage_line.work_start_date:
                        wip_days_diff = (today - last_stage_line.work_start_date).days

                    last_stage_line.write({
                        'to_stage_id': new_stage.id,
                        'complete_stage_date': today,
                        'stage_deadline_date': final_deadline,
                        'days_difference': (today - final_deadline).days if final_deadline else 0,
                        'stage_work_status': old_work_status,
                        'wip_days_difference': wip_days_diff,
                    })

                rec.stage_transition_ids.create({
                    'project_tracker_id': rec.id,
                    'from_stage_id': new_stage.id,
                    'work_start_date': today,
                })
        return res

    def open_revision_wizard(self):
        return {
            'type': 'ir.actions.act_window',
            'name': 'Add to Revision',
            'res_model': 'project.tracker.revision.wizard',
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_project_tracker_id': self.id,
                'default_available_stage_ids': self.available_stage_ids.ids,
                'default_customer_id': self.customer_id.id if self.customer_id else False,
                'default_quotation_id': self.sale_order_id.id if self.sale_order_id else False,
                'default_item_name': self.item_name,
                'default_qty': self.finish_material_qty,
                'default_planned_dispatch': self.planned_dispatch,
                'default_item_no': self.item_no,
            }
        }


    def action_mark_done(self):
        
        for rec in self:
            
            if rec.is_boughtout_req and rec.stage_selection in ('design', 'prod_dwngs') and not rec.boughtout_ids:
                raise ValidationError("Please add Boughtout Details before proceeding.")
            
            if rec.stage_selection == 'prod_dwngs' and (not rec.is_boughtout_req or not rec.boughtout_ids):
                raise ValidationError('Please add at least one Boughtout entry before proceeding to "Production Eng" stage.')
            
            if rec.stage_selection == 'prod_dwngs' and rec.boughtout_ids:
                group_boughtout_manager = self.env.ref('custom_project_tracker.group_boughtout_manager')
                boughtout_managers = self.env['res.users'].search([('groups_id', 'in', group_boughtout_manager.id)])
                if not boughtout_managers:
                    raise ValidationError("No Boughtout Manager Exists..")
                
                for manager in boughtout_managers: 
                    rec.env['mail.activity'].create({
                            'is_boughtout_activity':True,
                            'res_model_id': self.env.ref('custom_project_tracker.model_project_tracker').id,
                            'res_id': rec.id,
                            'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                            'summary': 'Add Boughtout Quantities Before Proceeding',
                            'note': (
                                        'Before proceeding beyond the "Production Eng" stage, '
                                        'please make sure that the required quantities are added in the "Boughtout Details" section. '
                                    ),
                            'user_id': manager.id,
                            'date_deadline':rec.production_eng_end_date,
                        })
                
            if rec.stage_selection == 'production_eng' and not rec.is_pr_created:
                raise ValidationError('Please create a Purchase Requisition (PR) before marking this as done.')         
            
            if rec.stage_selection == 'carpentry':
                
                group_project_tracker_manager = self.env.ref('custom_project_tracker.group_project_tracker_manager')
                tracker_manager_users = self.env['res.users'].search([('groups_id', 'in', group_project_tracker_manager.id)])
                activity_type = self.env.ref('mail.mail_activity_data_todo')
                activity_model = self.env['mail.activity']
                done_activity_model = self.env['mail.activity'].with_context(active_test=False)
                
                activities = activity_model.search([
                                ('is_pr_activity','=',True),
                                ('activity_type_id', '=', activity_type.id),
                                ('res_model', '=', 'project.tracker'),
                                ('res_id', '=', rec.id),
                                ('user_id', 'in', tracker_manager_users.ids)
                            ])
                
                # Search inactive (done) activities
                done_activities = done_activity_model.search([
                    ('is_pr_activity','=',True),
                    ('activity_type_id', '=', activity_type.id),
                    ('res_model', '=', 'project.tracker'),
                    ('res_id', '=', rec.id),
                    ('user_id', 'in', tracker_manager_users.ids),
                    ('active', '=', False),
                ])
                
                # combine both active and done
                all_activities = activities | done_activities
                
                if any(a.date_done for a in all_activities):
                    for activity in all_activities:
                        activity.action_done()
                
                if activities and all(not activity.date_done for activity in all_activities):
                        raise ValidationError("Please mark the Purchase Requisition (PR) activity as done before proceeding to the next stage")
    
                 
            current_stage = rec.stage_id
            if not current_stage:
                continue
            
            
            # Search next stage only within allowed stages
            next_stage = rec.available_stage_ids.filtered(
                lambda s: s.sequence > current_stage.sequence
            ).sorted(key=lambda s: s.sequence)
            if next_stage:
                rec.stage_id = next_stage[0].id

    def action_return_to_factory(self):
        for rec in self:
            rec.return_to_factory = True

    def action_dispatch(self):
        pass
    
    def action_create_purchase_requisition(self):
        
        for rec in self:
            
            if rec.is_pr_created:
                raise ValidationError("Purchase Requisition is already created for this record.")
        
            if any(rec.boughtout_ids.filtered(lambda b : b.quantity <= 0)):
                raise ValidationError('Add quantity in all Boughtout lines before marking this as done.')         
            
            group_boughtout_manager = self.env.ref('custom_project_tracker.group_boughtout_manager')
            boughtout_managers = self.env['res.users'].search([('groups_id', 'in', group_boughtout_manager.id)])
            activity_type = self.env.ref('mail.mail_activity_data_todo')
            activity_model = self.env['mail.activity']
            done_activity_model = self.env['mail.activity'].with_context(active_test=False)
            
            activities = self.env['mail.activity'].search([
                            ('is_boughtout_activity','=',True),
                            ('activity_type_id', '=', activity_type.id),
                            ('res_model', '=', 'project.tracker'),
                            ('res_id', '=', rec.id),
                            ('user_id', '=', boughtout_managers.ids),
                        ])
            
            # Search inactive (done) activities
            done_activities = done_activity_model.search([
                ('is_boughtout_activity','=',True),
                ('activity_type_id', '=', activity_type.id),
                ('res_model', '=', 'project.tracker'),
                ('res_id', '=', rec.id),
                ('user_id', 'in', boughtout_managers.ids),
                ('active', '=', False),
            ])
            
            # combine both active and done
            all_activities = activities | done_activities
            
            if any(a.date_done for a in all_activities):
                for activity in all_activities:
                    activity.action_done()
            
            if activities and all(not activity.date_done for activity in all_activities):
                raise ValidationError("Please mark the Boughtout activity as done before proceeding to the next stage.")
                    
            if not rec.is_carpentry_stage_available:
                raise ValidationError('Please add the Carpentry stage, as it is not available in the current stage selection.')
            
            if not rec.carpentry_end_date:
                raise ValidationError('Please fill in the Carpentry End Date before creating the Purchase Requisition.')
            
            group_project_tracker_manager = self.env.ref('custom_project_tracker.group_project_tracker_manager')
            tracker_manager_users = self.env['res.users'].search([('groups_id', 'in', group_project_tracker_manager.id)])
            
            if not tracker_manager_users:
                raise ValidationError("No Tracker Manager Exists..")

            activity_type = self.env.ref('mail.mail_activity_data_todo')
            for user in tracker_manager_users:
                rec.env['mail.activity'].create({
                'is_pr_activity': True,
                'activity_type_id': activity_type.id,
                'res_model_id': self.env.ref('custom_project_tracker.model_project_tracker').id,
                'res_id': rec.id,
                'activity_type_id': self.env.ref('mail.mail_activity_data_todo').id,
                'summary': "Bought-out items added – Please review PR",
                'note': "The quantity of bought outs has been added , please review PR for purchasing",
                'user_id': user.id,
                'date_deadline':rec.carpentry_end_date,
            })
            rec.is_pr_created = True
            
            

    