from odoo import models, fields, api
from odoo.exceptions import ValidationError

from odoo import models, fields, api
from odoo.exceptions import ValidationError

class ProjectTrackerStageTransition(models.Model):
    _name = 'project.tracker.stage.transition'
    _description = 'Project Tracker Stage Transition'
    _rec_name = 'name'
    _order = 'project_tracker_id, sequence, id'

    name = fields.Char(string='Name', compute='_compute_name', store=True)
    project_tracker_id = fields.Many2one('project.tracker', string="Tracker", ondelete='cascade')
    from_stage_id = fields.Many2one('project.stage', string="From Stage")
    to_stage_id = fields.Many2one('project.stage', string="To Stage")
    stage_deadline_date = fields.Date("Stage Deadline Date")
    complete_stage_date = fields.Date("Stage Completion Date")
    days_difference = fields.Integer("Overall Delay Difference (Days)")
    wip_days_difference = fields.Integer("WIP Difference (Days)")
    color = fields.Integer(string="Color", compute="_compute_color", store=True)
    drawing_image_ids = fields.Many2many('ir.attachment', related="project_tracker_id.drawing_image_ids", string="Drawing Images")
    sale_order_id = fields.Many2one('sale.order', related='project_tracker_id.sale_order_id', string="Quote No", store=True)
    work_start_date = fields.Date("Work Start Date")
    project_tracker_display_name = fields.Char(string="Project Tracker", compute="_compute_project_tracker_display_name", store=True)
    
    # Many2many dependency fields for Gantt (required format)
    predecessor_ids = fields.Many2many(
        'project.tracker.stage.transition',
        'stage_dependency_rel',
        'successor_id',
        'predecessor_id',
        string="Predecessor Stages",
        compute='_compute_dependencies',
        store=True
    )
    
    successor_ids = fields.Many2many(
        'project.tracker.stage.transition', 
        'stage_dependency_rel',
        'predecessor_id',
        'successor_id',
        string="Successor Stages"
    )
    
    # Sequence field for stage ordering
    sequence = fields.Integer(string="Sequence", default=10)

    stage_work_status = fields.Selection([
        ('done', 'Done'),
        ('revision', 'Revision'),
        ('delay', 'Delay or Late'),
        ('wip', 'WIP'),
        ('cancelled', 'Cancelled'),
    ], string="Status")

    @api.depends('stage_work_status')
    def _compute_color(self):
        for rec in self:
            status = (rec.stage_work_status or '').lower()
            if status == 'done':
                rec.color = 7
            elif status == 'revision':
                rec.color = 3
            elif status == 'delay':
                rec.color = 1
            elif status == 'wip':
                rec.color = 5
            elif status == 'cancelled':
                rec.color = 8
            else:
                rec.color = 0

    @api.depends('from_stage_id')
    def _compute_name(self):
        for rec in self:
            rec.name = f"{rec.from_stage_id.name or ''}"
    
    @api.depends('project_tracker_id', 'sequence')
    def _compute_dependencies(self):
        """Automatically set dependencies based on sequence within same project"""
        for rec in self:
            if rec.project_tracker_id:
                # Find previous stage in sequence for same project
                previous_stage = self.search([
                    ('project_tracker_id', '=', rec.project_tracker_id.id),
                    ('sequence', '<', rec.sequence),
                    ('id', '!=', rec.id)
                ], order='sequence desc', limit=1)
                
                if previous_stage:
                    rec.predecessor_ids = [(6, 0, [previous_stage.id])]
                else:
                    rec.predecessor_ids = [(6, 0, [])]
            else:
                rec.predecessor_ids = [(6, 0, [])]

    @api.model
    def create(self, vals):
        """Auto-assign sequence if not provided"""
        if 'sequence' not in vals and vals.get('project_tracker_id'):
            # Get max sequence for this project and add 10
            max_seq = self.search([
                ('project_tracker_id', '=', vals['project_tracker_id'])
            ], order='sequence desc', limit=1)
            vals['sequence'] = (max_seq.sequence + 10) if max_seq else 10
        
        return super().create(vals)

    @api.model
    def recompute_all_dependencies(self):
        """Method to recompute all dependencies - useful for data migration"""
        all_records = self.search([])
        for project_tracker in self.env['project.tracker'].search([]):
            stages = self.search([
                ('project_tracker_id', '=', project_tracker.id)
            ], order='sequence, id')
            
            previous = False
            for stage in stages:
                if previous:
                    stage.predecessor_ids = [(6, 0, [previous.id])]
                else:
                    stage.predecessor_ids = [(6, 0, [])]
                previous = stage


    @api.onchange('stage_deadline_date', 'complete_stage_date', 'work_start_date' )
    def _onchange_stage_id(self):
        if self.stage_deadline_date and self.complete_stage_date:
            self.days_difference = (self.complete_stage_date - self.stage_deadline_date).days
        if self.work_start_date and self.complete_stage_date:
            self.wip_days_difference = (self.complete_stage_date - self.work_start_date).days

    @api.depends('project_tracker_id', 'project_tracker_id.item_no', 'project_tracker_id.item_name', 'project_tracker_id.dispatch_end_date')
    def _compute_project_tracker_display_name(self):
        for record in self:
            if record.project_tracker_id:
                tracker = record.project_tracker_id
                dispatch_date = tracker.dispatch_end_date or ''
                if dispatch_date:
                    dispatch_date = dispatch_date.strftime('%d/%m/%Y')
                    record.project_tracker_display_name = f"{tracker.item_no} {tracker.item_name} ({dispatch_date})"
                else:
                    record.project_tracker_display_name = f"{tracker.item_no} {tracker.item_name}"
            else:
                record.project_tracker_display_name = ""