from odoo import models, fields, api
from odoo.exceptions import ValidationError, UserError


class BoughtoutBoughtout(models.Model):
    _name = "boughtout.boughtout"
    _description = 'Project Boughtout'
    _rec_name = 'boughtout_id'
    
    boughtout_id = fields.Many2one("product.template", string="Required", required=True,  domain="[('id', 'not in', used_boughtout_ids),('is_boughtout', '=', True)]" )
    scope = fields.Selection([("by_customer" , "By Customer"),
                              ("by_proform ", "By Proform")], string="Scope", required=True)
    boughtout_selection = fields.Char("Selection", required=True)
    quantity = fields.Float("Quantity")
    order_date = fields.Date("Order Date")
    receipt_date = fields.Date("Receipt Date")
    project_tracket_id = fields.Many2one("project.tracker", string="Project Tracker")
    activity_created = fields.Boolean(string="Activity Created", default=False)
    used_boughtout_ids = fields.Many2many('product.template', compute="_compute_used_boughtout_ids")
    qty_add_flag = fields.Boolean(string="Qty Add Flag",compute="_compute_qty_add_flag",default=False)
    
    @api.constrains('quantity')
    def _check_quantity(self):
        for rec in self:
            if rec.quantity < 0:
                raise ValidationError("Negative quantity is not allowed.")                
                
    @api.model
    def create(self,vals):
        tracker_id = vals.get('project_tracket_id')
        project_tracker_id = self.env['project.tracker'].browse(tracker_id)
        if project_tracker_id.stage_selection == 'production_eng':
            raise ValidationError(f'You cannot create boughtout in {project_tracker_id.stage_selection}(Production Eng) stage.')
        res = super(BoughtoutBoughtout,self).create(vals)
        return res
        
    
    def _compute_qty_add_flag(self):
        for rec in self:
            flag = False
            if rec.project_tracket_id and rec.project_tracket_id.stage_selection == 'production_eng':
                flag = True
            rec.qty_add_flag = flag
        
    @api.depends('boughtout_id')
    def _compute_used_boughtout_ids(self):
        for rec in self:
            rec.used_boughtout_ids = False
            boughtouts = rec.project_tracket_id.boughtout_ids
            if boughtouts:
                existing_boughtouts = boughtouts.mapped('boughtout_id.id')
                rec.used_boughtout_ids = [(6,0,existing_boughtouts)]
            
            
            