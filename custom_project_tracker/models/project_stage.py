from odoo import models, fields

class ProjectStage(models.Model):
    _name = 'project.stage'
    _description = 'Project Stage'
    _order = 'sequence'

    name = fields.Char("Stage Name", required=True)
    sequence = fields.Integer("Sequence", default=1)
    fold = fields.Boolean("Is Folded", default=False)
    stage_selection = fields.Selection([
        ('design', 'Design'),
        ('prod_dwngs', 'Prod Dwngs'),
        ('production_eng', 'Production Eng'),
        ('carpentry', 'Carpentry'),
        ('machining', 'Machining'),
        ('pre_assembly', 'Pre Assembly'),
        ('metal', 'Metal'),
        ('stone', 'Stone'),
        ('polishing', 'Polishing'),
        ('upholstery', 'Upholstery'), 
        ('assembly', 'Assembly'),
        ('quality_check', 'Quality Check'),
        ('packaging', 'Packaging'),
        ('ready_for_dispatch', 'Ready For Dispatch'),
        ('dispatch', 'Dispatch'),
        ('installation', 'Installation'),
        ('hold','Hold'),
    ], string="Stage Selection")



    required_activity = fields.Boolean(string='Required Activity', 
                                      help='If checked, an activity will be created when a project enters this stage')
    user_id = fields.Many2one('res.users', string='Responsible User',
                                         help='User responsible for completing the activity')
    activity_summary = fields.Char(string='Activity Summary', 
                                  help='Short title for the activity')
    activity_note = fields.Text(string='Activity Instructions', 
                               help='Detailed instructions or notes for the activity')
    activity_deadline_days = fields.Integer(string='Deadline (Days)', default=3,
                                          help='Number of days to set as the activity deadline from stage entry date')
    activity_type_id = fields.Many2one('mail.activity.type', string='Activity Type', 
                                      default=lambda self: self.env.ref('mail.mail_activity_data_todo', False))