from odoo import models, fields,api

class ResPartner(models.Model):
    _inherit = 'res.partner'

    contact_type = fields.Selection([
        ('contractor', 'Contractor'),
        ('employee', 'Employee'),
        ('vendor', 'Vendor'),
        ('customer', 'Customer')
    ], string="Contact Type", default='customer', tracking=True)
    type = fields.Selection(selection='_get_new_contact_selection', string='Address Type', default='invoice')
    poc_accounts = fields.Char(string="POC-Accounts")
    poc_accounts_phone = fields.Char(string="Phone No.")
    poc_site = fields.Char(string="POC-Site")
    poc_site_phone = fields.Char(string="Phone No.")
    poc_other = fields.Char(string="POC-Other")
    poc_other_phone = fields.Char(string="Phone No.")

    @api.model
    def _get_new_contact_selection(self):
        selection = [
            ('invoice','Billing Address'),
            ('other','Site Address'),

        ]
        return selection