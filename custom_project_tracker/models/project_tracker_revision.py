from odoo import models, fields

class ProjectTrackerRevision(models.Model):
    _name = 'project.tracker.revision'
    _description = 'Tracker Revisions'
    _order = 'date desc'

    tracker_id = fields.Many2one('project.tracker', string='Tracker')
    name = fields.Char(required=True)
    date = fields.Date(required=True)
    customer_id = fields.Many2one('res.partner', string='Client Name', store=True)
    quotation_id = fields.Many2one('sale.order', string='Quotation', store=True)
    item_name = fields.Char(string='Item Name', store=True)
    qty = fields.Float(string='Quantity', store=True)
    item_no = fields.Char("Item No.",)
    planned_dispatch = fields.Date(string='Planned Dispatch', store=True)