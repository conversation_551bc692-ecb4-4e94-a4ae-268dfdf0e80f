from odoo import models, fields, api, _
from dateutil.relativedelta import relativedelta


class SaleOrder(models.Model):
    _inherit = "sale.order"
    
    planned_dispatch = fields.Date(string="Planned Dispatch",required=True, tracking=True)
    
    project_tracker_ids = fields.One2many(
        'project.tracker', 'sale_order_id', string="Project Trackers"
    )

    designer_id = fields.Many2one(
        'res.users',
        string="Designer",
        required=True,
        domain="[('is_designer', '=', True)]"
    )

    work_category = fields.Selection([
        ('millwork', 'Millwork'),
        ('furniture', 'Furniture'),
        ('mw_fur', 'MW + Fur'),
    ], string="Work Category")

    is_quote_valid = fields.Selection([
        ('hold', 'Hold'),
        ('cancel', 'Cancel'),
    ], string="Is Quote Valid")

    def action_view_project_trackers(self):
        self.ensure_one()
        action = self.env.ref('custom_project_tracker.action_project_tracker').read()[0]
        action['domain'] = [('sale_order_id', '=', self.id)]
        action['context'] = {
            'default_sale_order_id': self.id,
            'group_by': ['customer_id', 'sale_order_id']
        }
        return action


    project_tracker_count = fields.Integer(
        string='Project Tracker Count',
        compute='_compute_project_tracker_count'
    )

    @api.depends('order_line')  # You can also depend directly on related trackers
    def _compute_project_tracker_count(self):
        for order in self:
            order.project_tracker_count = self.env['project.tracker'].search_count([
                ('sale_order_id', '=', order.id)
            ])

    @api.onchange('order_line')
    def _onchange_order_line_item_no(self):
        self._recalculate_item_nos()



    def _recalculate_item_nos(self):
        for order in self:
            # Sort only lines that are not section/note lines
            valid_lines = order.order_line.filtered(lambda l: not l.display_type)
            total = len(valid_lines)
            for index, line in enumerate(valid_lines, start=1):
                line.item_no = f"{index:02}/{total:02}"


    @api.model
    def create(self, vals):
        record = super(SaleOrder, self).create(vals)
        record._notify_stakeholders_quote_created()
        return record

    def write(self, vals):
        res = super(SaleOrder, self).write(vals)
        if 'is_quote_valid' in vals and vals['is_quote_valid'] in ['hold', 'cancel']:
            for rec in self:
                rec._notify_quote_hold_cancel()
        return res

    def _notify_stakeholders_quote_created(self):
        user_groups = [
            'custom_project_tracker.group_proform_md',
            'custom_project_tracker.group_proform_coo',
            'custom_project_tracker.group_proform_pmo',
            'custom_project_tracker.group_project_tracker_designer',
            'custom_project_tracker.group_proform_finance',
            'custom_project_tracker.group_proform_accounts',
            'custom_project_tracker.group_proform_scm',
            'custom_project_tracker.group_project_tracker_pd_hod',
        ]
        message = _("New quote is created - Customer: %s, Quote No: %s") % (self.partner_id.name, self.name)
        self._send_group_notifications(user_groups, message)

    def _notify_quote_hold_cancel(self):
        status = dict(self._fields['is_quote_valid'].selection).get(self.is_quote_valid)
        content = _("Customer %s, Quote %s is '%s'") % (self.partner_id.name, self.name, status)
        user_groups = [
            'custom_project_tracker.group_proform_pmo',
            'custom_project_tracker.group_project_tracker_designer',
            'custom_project_tracker.group_project_tracker_pd_hod',
            'custom_project_tracker.group_proform_finance',
            'custom_project_tracker.group_proform_accounts',
        ]
        self._send_group_notifications(user_groups, content)

    def _send_group_notifications(self, group_xml_ids, message_body):
        users = self.env['res.users'].sudo().search([
            ('groups_id', 'in', [self.env.ref(gid).id for gid in group_xml_ids])
        ])
        for user in users:
            self.message_post(
                body=message_body,
                partner_ids=[user.partner_id.id],
                message_type='notification',
                subtype_xmlid='mail.mt_note'
            )


    def action_confirm(self):
        res = super().action_confirm()
        self._recalculate_item_nos()

        project = self.env['project.project'].create({
            'name': self.name,
        })
        # Get relevant data from sale order
        customer_id = self.partner_id
        order_date = self.date_order
        remark = self.note
        responsible_user = self.env.user
        
        # Get default stage for project tracker
        default_stage = self.env['project.stage'].search([], order="sequence asc", limit=1)
        
        # Create project tracker records for each order line
        project_tracker_obj = self.env['project.tracker']
        created_trackers = []
        
        for line in self.order_line:
            # Skip service products or products with no tracking needed
            if line.product_id.type == 'service':
                continue
            
            item_name = line.product_id.name
            item_no = line.item_no  
            
            # Prepare values for the project tracker
            tracker_vals = {
                'customer_id': customer_id.id,
                'sale_order_id': self.id,  # Use sale_order_id directly
                'planned_dispatch': self.planned_dispatch,
                'designer_id': self.designer_id.id,
                'work_category': self.work_category,
                'item_no': item_no,
                'item_no_detail': line.item_no_detail,
                'item_name': item_name,
                'finish_material': line.item_finish or '',
                'finish_material_qty': line.product_uom_qty,
                'design_end_date': line.design_end_date,
                'stage_id': default_stage.id,
                'date': order_date,
                'remarks': remark,
                'area_layout': line.area_as_per_layout,
                'user_id': responsible_user.id,
                'work_status': 'wip',
            }
            
            # Create the project tracker record
            tracker = project_tracker_obj.create(tracker_vals)
            created_trackers.append(tracker)
            
            # Log the creation in the chatter
            tracker.message_post(
                body=f"Created automatically from Sales Order {self.name}",
                message_type="notification"
            )
            
            # Create activity if default stage requires it
            tracker._create_stage_activity()
        
        # Show success message if trackers were created
        if created_trackers:
            message = _(f"Created {len(created_trackers)} project tracker records for this sales order.")
            self.message_post(body=message)
            
        return res

    def create(self, vals):
        order = super().create(vals)
        order._recalculate_item_nos()
        return order

    def write(self, vals):
        res = super().write(vals)
        for order in self:
            order._recalculate_item_nos()
        return res

class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    design_end_date = fields.Date(string="Design End Date", required=True)
    discount = fields.Float(
        string="Discount (%)",
        digits=(16, 4),
    )
    item_no = fields.Char(string="Item No", store=True)
    gross_price = fields.Float(string="Gross Price", compute="_compute_gross_price", save=True)
    area_as_per_layout = fields.Char(string="Layout Area")
    item_finish = fields.Char(string="Item Finish")
    item_no_detail = fields.Selection([
        ('elevations', 'Elevations'),
        ('parts', 'Parts'),
        ('additions', 'Additions'),
        ('combined_with', 'Combined with'),
        ('site_return', 'Site Return'),
    ], string="Item No. Detail")
    size = fields.Char(string="Size")
    discount_amount = fields.Float(string="Discount Amount", compute="_compute_discount_amount", save=True)
    final_amount = fields.Float(string="Final Amount", compute="_compute_final_amount", save=True)

    @api.depends('price_unit', 'product_uom_qty')
    def _compute_gross_price(self):
        for line in self:
            line.gross_price = line.price_unit * line.product_uom_qty
            
    @api.depends('price_unit', 'product_uom_qty', 'discount')
    def _compute_discount_amount(self):
        for line in self:
            if line.discount and line.product_uom_qty:
                discount_total = (line.price_unit * line.product_uom_qty) * (line.discount / 100)
                line.discount_amount = discount_total / line.product_uom_qty
            else:
                line.discount_amount = 0.0

            
    @api.depends('price_unit', 'discount_amount')
    def _compute_final_amount(self):
        for line in self:
            if line.discount_amount > 0:
                line.final_amount = line.price_unit - line.discount_amount
            else:
                line.final_amount = line.price_unit