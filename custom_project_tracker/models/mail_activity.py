from odoo import models, fields, api

class MailActivity(models.Model):
    _inherit = 'mail.activity'
    
    is_boughtout_activity = fields.<PERSON><PERSON>an(string="Is Boughtout Activity", default=False)
    is_hold_activity = fields.Boolean(string="Is Hold Activity",default=False)
    is_pr_activity = fields.Boolean(string="Is PR Activity", default=False)
    
    @api.model
    def get_activity_data(self, res_model, res_id, domain=None):
        """Override to add group access information"""
        result = super().get_activity_data(res_model, res_id, domain)
        
        user_has_cancel_rights = self.env.user.has_group('custom_project_tracker.group_activity_cancel_rights')
        
        for activity_data in result.get('activities', []):
            activity_data['show_cancel_button'] = user_has_cancel_rights
            
        return result

    def read(self, fields=None, load='_classic_read'):
        """Override read method to include group access info"""
        result = super().read(fields, load)
        
        user_has_cancel_rights = self.env.user.has_group('custom_project_tracker.group_activity_cancel_rights')
        
        if isinstance(result, list):
            for record in result:
                record['show_cancel_button'] = user_has_cancel_rights
        else:
            result['show_cancel_button'] = user_has_cancel_rights
            
        return result