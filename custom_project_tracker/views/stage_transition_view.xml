<odoo>

    <record id="view_project_tracker_stage_transition_tree" model="ir.ui.view">
        <field name="name">project.tracker.stage.transition.tree</field>
        <field name="model">project.tracker.stage.transition</field>
        <field name="arch" type="xml">
            <list default_order="create_date desc" create="0" edit="0">
                <field name="project_tracker_id"/>
                <field name="sale_order_id"/>
                <field name="from_stage_id"/>
                <field name="to_stage_id"/>
                <field name="work_start_date"/>
                <field name="complete_stage_date"/>
                <field name="stage_deadline_date"/>
                <field name="wip_days_difference"/>
                <field name="days_difference"/>
                <field name="stage_work_status"/>
            </list>
        </field>
    </record>

    <record id="view_project_tracker_stage_transition_form" model="ir.ui.view">
        <field name="name">project.tracker.stage.transition.form</field>
        <field name="model">project.tracker.stage.transition</field>
        <field name="arch" type="xml">
            <form string="Project Tracker Stage Transition" create="0" edit="0">
                <sheet>
                    <group>
                        <group string="Basic Info">
                            <field name="project_tracker_id" />
                            <field name="sale_order_id"/>
                            <field name="from_stage_id"/>
                            <field name="to_stage_id"/>
                            <field name="stage_work_status"/>
                            <field name="drawing_image_ids" widget="many2many_binary" />
                        </group>
                        <group string="Dates and Durations">
                            <field name="work_start_date"/>
                            <field name="complete_stage_date"/>
                            <field name="stage_deadline_date"/>
                            <field name="wip_days_difference"/>
                            <field name="days_difference"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_project_tracker_stage_transition_search" model="ir.ui.view">
        <field name="name">project.tracker.stage.transition.search</field>
        <field name="model">project.tracker.stage.transition</field>
        <field name="arch" type="xml">
            <search>
                <field name="project_tracker_id"/>
                <field name="from_stage_id"/>
                <field name="to_stage_id"/>
                <field name="sale_order_id"/>
                <field name="stage_deadline_date"/>
                <field name="complete_stage_date"/>
                <field name="stage_work_status"/>
                <field name="days_difference"/>
                <group string="Group By">
                    <filter string="Project Tracker" name="project_tracker_id" context="{'group_by': 'project_tracker_id'}"/>
                    <filter string="Quote No" name="sale_order_id" context="{'group_by': 'sale_order_id'}"/>
                    <filter string="From Stage" name="from_stage_id" context="{'group_by': 'from_stage_id'}"/>
                    <filter string="To Stage" name="to_stage_id" context="{'group_by': 'to_stage_id'}"/>
                    <filter string="Stage Deadline Date" name="stage_deadline_date" context="{'group_by': 'stage_deadline_date'}"/>
                    <filter string="Stage Complete Date" name="complete_stage_date" context="{'group_by': 'complete_stage_date'}"/>
                    <filter string="Stage Work Status" name="stage_work_status" context="{'group_by': 'stage_work_status'}"/>
                    <filter string="Overall Delay Days Difference" name="days_difference" context="{'group_by': 'days_difference'}"/>
                    <filter string="Project Tracker Display Name" name="project_tracker_display_name" context="{'group_by': 'project_tracker_display_name'}"/>
                </group>
            </search>
        </field>
    </record>



    <record id="view_project_tracker_stage_transition_gantt" model="ir.ui.view">
        <field name="name">project.tracker.stage.transition.gantt</field>
        <field name="model">project.tracker.stage.transition</field>
        <field name="arch" type="xml">
            <gantt date_start="work_start_date" date_stop="complete_stage_date" string="Project Timeline" color="color" delete="0" dependency_field="predecessor_ids" dependency_inverted_field="successor_ids" default_group_by="project_tracker_display_name" disable_drag_drop="1" create="0" edit="0">
                <field name="project_tracker_display_name"/>
                <field name="from_stage_id"/>
                <field name="to_stage_id"/>
                <field name="complete_stage_date"/>
                <field name="stage_deadline_date"/>
                <field name="stage_work_status"/>
                <field name="name"/>
                <field name="days_difference"/>
                <field name="wip_days_difference"/>
                <field name="predecessor_ids"/>
                <field name="successor_ids"/>
                <field name="sequence"/>
                <templates>
                    <div t-name="gantt-popover">
                        <div t-if="stage_work_status">
                            <strong>Work Status: </strong>
                            <t t-esc="stage_work_status"/>
                        </div>
                        <div t-if="work_start_date" class="mb-1">
                            <strong>Start Date: </strong>
                            <t t-esc="work_start_date.toFormat('dd/MM/yyyy')"/>
                        </div>
                        <div t-if="complete_stage_date" class="mb-1">
                            <strong>Complete Date: </strong>
                            <t t-esc="complete_stage_date.toFormat('dd/MM/yyyy')"/>
                        </div>
                        <div t-if="wip_days_difference">
                            <strong>WIP Days Diff: </strong>
                            <t t-esc="wip_days_difference"/>
                        </div>
                        <div t-if="stage_deadline_date">
                            <strong>Current Stage Deadline Date: </strong>
                            <t t-esc="stage_deadline_date.toFormat('dd/MM/yyyy')"/>
                        </div>
                        <div t-if="days_difference">
                            <strong>Overall Delay Days Diff: </strong>
                            <t t-esc="days_difference"/>
                            <strong>project_tracker_display_name: </strong>
                            <t t-esc="project_tracker_display_name"/>
                        </div>
                    </div>
                </templates>
            </gantt>
        </field>
    </record>


    <record id="view_project_tracker_stage_transition_pivot" model="ir.ui.view">
        <field name="name">project.tracker.stage.transition.pivot</field>
        <field name="model">project.tracker.stage.transition</field>
        <field name="arch" type="xml">
            <pivot string="Stage Transition Analysis">
                <field name="project_tracker_id" type="measure"/>
                <field name="sale_order_id" type="row"/>
                <field name="from_stage_id" type="col"/>
                <field name="to_stage_id" type="col"/>
                <field name="days_difference" type="col"/>
                <field name="stage_work_status" type="row"/>
            </pivot>
        </field>
    </record>


    <record id="view_project_tracker_stage_transition_action" model="ir.actions.act_window">
        <field name="name">Stage Transitions</field>
        <field name="res_model">project.tracker.stage.transition</field>
        <field name="view_mode">gantt,list,form,pivot</field>
        <field name="context">{'group_by': ['sale_order_id', 'project_tracker_display_name']}</field>
    </record>

    <record id="action_open_stage_transitions_gantt" model="ir.actions.act_window">
        <field name="name">Stage Transitions Gantt</field>
        <field name="res_model">project.tracker.stage.transition</field>
        <field name="view_mode">gantt,list,form</field>
        <field name="domain">[('sale_order_id', '=', active_id)]</field>
        <field name="context">{'default_sale_order_id': active_id, 'group_by': ['sale_order_id', 'project_tracker_id']}</field>
        <field name="target">current</field>
    </record>

    <menuitem id="menu_project_stage_dashboard_main" name="Dashboard" parent="menu_project_tracker_root"/>
    <menuitem id="menu_project_tracker_stage_transition" name="Stage Timeline Dashboard" parent="custom_project_tracker.menu_project_stage_dashboard_main" action="view_project_tracker_stage_transition_action" sequence="20"/>

</odoo>
