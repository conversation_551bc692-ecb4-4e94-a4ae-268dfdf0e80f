<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_partner_form_inherit_contact" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.contact.</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base.view_partner_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='vat']" position="before">
                <group>
                    <field name="contact_type" widget="radio" options="{'horizontal': true}" required="1"/>
                </group>
            </xpath>
            <xpath expr="//field[@name='company_type']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='sales_purchases']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//page[@name='internal_notes']" position="attributes">
                <attribute name="string">Remarks</attribute>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']//form//field[@name='name']" position="attributes">
                <attribute name="required">type in ('invoice','other')</attribute>
            </xpath>
            <xpath expr="//page[@name='contact_addresses']//form//div[@class='text-muted oe_edit_only']"
                   position="replace">
            </xpath>
            <xpath expr="//field[@name='phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='phone']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//label[@for='email']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='website']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='category_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='mobile']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='vat']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='function']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='title']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
            <xpath expr="//field[@name='street']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='city']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='state_id']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='zip']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="required">True</attribute>
            </xpath>
            <xpath expr="//field[@name='function']" position="after">
                <field name="poc_accounts"/>
                <field name="poc_accounts_phone"/>
                <field name="poc_accounts_phone" string="POC-Accounts Ph."/>
                <field name="poc_site"/>
                <field name="poc_site_phone"/>
                <field name="poc_other"/>
                <field name="poc_other_phone"/>
            </xpath>
            <xpath expr="//field[@name='vat']" position="after">
                <group>
                    <field name="phone" required="1"/>
                    <field name="email" required="1"/>
                </group>
            </xpath>
        </field>
    </record>
    <record id="view_partner_base_vat_form_inherited" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.contact.</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="base_vat.view_partner_base_vat_form"/>
        <field name="arch" type="xml">
            <xpath expr="//label[@for='vat']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_partner_form_inherit_contact_project_tracker" model="ir.ui.view">
        <field name="name">res.partner.form.inherit.contact.project.tracker</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="calendar.view_partners_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='schedule_meeting']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_partners_form_crm_2" model="ir.ui.view">
        <field name="name">view.res.partner.form.crm.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="crm.view_partners_form_crm1"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_opportunity']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="res_partner_view_buttons_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.sales.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="sale.res_partner_view_buttons"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_sale_order']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="partner_view_buttons_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.invoices.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.partner_view_buttons"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_partner_invoices']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_partner_property_form_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.page.invoices.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="account.view_partner_property_form"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='accounting']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_task_partner_info_form_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.project.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="project.view_task_partner_info_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_tasks']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="view_partner_stock_warnings_form_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.stock.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="stock.view_partner_stock_warnings_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_stock_lots']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="vres_partner_view_purchase_buttons_form_inherited" model="ir.ui.view">
        <field name="name">view.res.partner.views.purchase.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="purchase.res_partner_view_purchase_buttons"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='%(purchase.act_res_partner_2_purchase_order)d']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
    <record id="res_partner_view_purchase_buttons_stock_inherit" model="ir.ui.view">
        <field name="name">view.res.partner.views.purchase.stock.inherited2</field>
        <field name="model">res.partner</field>
        <field name="inherit_id" ref="purchase_stock.res_partner_view_purchase_buttons_inherit"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='%(purchase_stock.action_purchase_vendor_delay_report)d']"
                   position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>
</odoo>