<?xml version="1.0" encoding="utf-8"?>
<odoo>


    <record id="view_project_boughtout_form" model="ir.ui.view">
        <field name="name">project.boughtout.form</field>
        <field name="model">boughtout.boughtout</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group string="Boughtout Information">
                            <field name="boughtout_id"/>
                            <field name="scope"/>
                        </group>

                        <group string="Product Information">
                            <field name="quantity"/>
                            <field name="order_date"/>
                            <field name="receipt_date"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="action_boughtout_products" model="ir.actions.act_window">
        <field name="name">Boughtout Products</field>
        <field name="res_model">product.template</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('is_boughtout', '=', True)]</field>
        <field name="context">{'default_is_boughtout': True}</field>
    </record>

    <menuitem id="menu_project_boughtout" name="Boughtout" parent="menu_project_tracker_configuration" action="action_boughtout_products"/>


</odoo>