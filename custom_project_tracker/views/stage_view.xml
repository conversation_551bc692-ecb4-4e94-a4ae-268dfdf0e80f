<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <record id="view_project_stage_list" model="ir.ui.view">
        <field name="name">project.stage.list</field>
        <field name="model">project.stage</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
            </list>
        </field>
    </record>

    <record id="view_project_stage_form" model="ir.ui.view">
        <field name="name">project.stage.form</field>
        <field name="model">project.stage</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group string="Stage Info">
                            <field name="name"/>
                            <field name="sequence"/>
                            <field name="fold"/>
                            <field name="stage_selection"/>
                        </group>

                        <group string="Activity Settings">
                            <field name="required_activity"/>
                            <field name="user_id" invisible="not required_activity"/>
                            <field name="activity_type_id" invisible="not required_activity"/>
                            <field name="activity_summary" invisible="not required_activity"/>
                            <field name="activity_note" invisible="not required_activity"/>
                            <field name="activity_deadline_days" invisible="not required_activity"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


    <record id="action_project_stage" model="ir.actions.act_window">
        <field name="name">Stages</field>
        <field name="res_model">project.stage</field>
        <field name="view_mode">list,form</field>
    </record>

    <menuitem id="menu_project_stage" name="Stage" parent="menu_project_tracker_configuration" action="action_project_stage"/>

</odoo>