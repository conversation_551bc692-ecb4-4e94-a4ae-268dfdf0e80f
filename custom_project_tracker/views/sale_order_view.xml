<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_order_form_inherit_project_tracker" model="ir.ui.view">
        <field name="name">sale.order.form.inherit.project.tracker</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_invoice']" position="after">
                <button name="action_view_project_trackers" string="Project Tracker" type="object" class="oe_stat_button" icon="fa-puzzle-piece" invisible="project_tracker_count == 0">
                    <field name="project_tracker_count" string="Project Tracker" widget="statinfo"/>
                </button>
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='price_subtotal']" position="after">
                <field name="design_end_date"/>
            </xpath>
            <xpath expr="//field[@name='order_line']/list/field[@name='product_id']" position="before">
                <field name="item_no" optional="show"/>
                <field name="item_no_detail" required="1" optional="show"/>
            </xpath>

            <xpath expr="//field[@name='order_line']" position="attributes">
                <attribute name="readonly">state in ('cancel', 'sale') or locked</attribute>
            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='product_template_id']" position="attributes">
                <attribute name="string">Item Name</attribute>

            </xpath>

            <xpath expr="//field[@name='order_line']/list/field[@name='product_template_id']" position="after">
                <field name="area_as_per_layout" optional="show" required="1"/>
                <field name="size" optional="show" required="1"/>
                <field name="item_finish" optional="show" required="1"/>
            </xpath>

            <xpath expr="//field[@name='product_packaging_qty']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='product_packaging_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='qty_delivered']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='qty_invoiced']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='product_uom'][1]" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='product_uom'][2]" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='price_unit']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='tax_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='discount']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='price_subtotal']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            <xpath expr="//field[@name='sale_order_template_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='validity_date']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//group[@name='order_details']/field[@name='validity_date']" position="before">
                <field name="planned_dispatch" />
            </xpath>

            <xpath expr="//field[@name='name']" position="attributes">
                <attribute name="readonly">0</attribute>
            </xpath>

            <xpath expr="//button[@name='action_view_project_trackers']" position="attributes">
                <attribute name="groups">custom_project_tracker.group_project_tracker_manager</attribute>
            </xpath>

            <xpath expr="//page[@name='other_information']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//page[@name='customer_signature']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='pricelist_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='payment_term_id']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='action_preview_sale_order']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@id='create_invoice_percentage']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@id='create_invoice']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//button[@name='action_preview_sale_order']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <xpath expr="//field[@name='partner_id']" position="after">
                <field name="designer_id"></field>
                <field name="work_category"></field>
            </xpath>

            <xpath expr="//field[@name='date_order']" position="after">
                <field name="is_quote_valid"></field>
            </xpath>

            <xpath expr="//group[@name='order_details']/div[2]" position="replace">
                <div class="o_td_label" invisible="state in ['draft', 'sent']">
                    <label for="date_order" string="Quotation Date"/>
                </div>
            </xpath>

            <xpath expr="//list/field[@name='price_unit']" position="attributes">
                <attribute name="string">MRP</attribute>
            </xpath>

            <xpath expr="//list/field[@name='price_unit']" position="after">
                <field name="gross_price" string="Gross Price" readonly="1"/>
            </xpath>

            <xpath expr="//list/field[@name='discount']" position="after">
                <field name="discount_amount" string="Discount Amount" readonly="1"/>
                <field name="final_amount" string="Final Amount" readonly="1"/>
            </xpath>

            <xpath expr="//list/field[@name='price_subtotal']" position="attributes">
                <attribute name="string">Net Price (INR)</attribute>
            </xpath>
        </field>
    </record>


    <record id="inherit_hide_quote_builder_tab" model="ir.ui.view">
        <field name="name">sale.order.form.hide.pdf.quote.builder</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_pdf_quote_builder.sale_order_form_inherit_sale_pdf_quote_builder"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='pdf_quote_builder']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>

    <record id="inherit_hide_delivery_button_from_sale_stock_view" model="ir.ui.view">
        <field name="name">sale.order.form.hide.delivery</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_stock.view_order_form_inherit_sale_stock"/>
        <field name="arch" type="xml">
            <xpath expr="//button[@name='action_view_delivery']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>



    <record id="inherit_hide_optional_product_tab" model="ir.ui.view">
        <field name="name">sale.order.form.hide.optional.product.tab</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale_management.sale_order_form_quote"/>
        <field name="arch" type="xml">
            <xpath expr="//page[@name='optional_products']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>
        </field>
    </record>


</odoo>