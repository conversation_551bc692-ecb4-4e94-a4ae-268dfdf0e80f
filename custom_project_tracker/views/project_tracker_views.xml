<odoo>
    <record id="view_project_tracker_form" model="ir.ui.view">
        <field name="name">project.tracker.form</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <form string="Project Tracker" class="o_sale_order">
                <header>
                    <field name="stage_id" widget="statusbar_duration"
                           options="{'clickable': '1', 'fold_field': 'fold'}" readonly="1"/>
                    <button name="action_open_stage_wizard" string="Set Allowed Stages" type="object"
                            class="oe_stat_button" groups="custom_project_tracker.group_project_tracker_manager"
                            invisible="stage_selection not in ('production_eng')"/>
                    <button name="action_mark_done" type="object" string="Mark Done" class="btn-primary"
                            invisible="stage_selection in ('installation')"/>
                    <button name="action_return_to_factory" type="object" string="Return to factory" class="btn-primary"
                            invisible="stage_selection not in ('installation') or return_to_factory"/>
                    <button name="action_dispatch" type="object" string="Dispatch now" class="btn-primary"
                            invisible="stage_selection not in ('dispatch')"/>
                    <button name="action_approve_hold_item" type="object" string="Ok" class="btn-primary"
                            invisible="hold_requested == False"/>
                    <button name="action_reject_hold_item" type="object" string="Not Ok" class="btn-primary"
                            invisible="hold_requested == False"/>
                    <button name="action_create_purchase_requisition" type="object" string="Create PR" class="btn-primary" invisible="stage_selection not in ('production_eng')"/>

                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_open_project" type="object" string="Project" class="oe_stat_button"
                                icon="fa-puzzle-piece">
                            <field name="project_count" widget="statinfo" string="Projects"/>
                        </button>

                        <button name="action_open_stage_transitions_gantt" string="Stage Gantt" type="object"
                                class="oe_stat_button" icon="fa-bar-chart"/>
                    </div>

                    <widget name="web_ribbon" title="Hold" bg_color="text-bg-danger"
                            invisible="not hold"/>
                    <!--                    <widget name="web_ribbon" title="Requested For Hold" bg_color="text-bg-danger"-->
                    <!--                            invisible="hold_requested == False"/>-->
                    <group>
                        <group string="General Information">
                            <field name="active" readonly="1" invisible="1"/>
                            <field name="customer_id" readonly="1" string="Customer"/>
                            <field name="sale_order_id" readonly="1" string="Quote No"/>
                            <field name="date" readonly="1" string="Quote Date"/>
                            <field name="planned_dispatch" readonly="1" string="Planned Dispatch"/>
                            <field name="drgs_recd_dt" string="Drgs Recd Dt"
                                   invisible="stage_selection not in ('production_eng')"/>
                            <field name="designer_id" readonly="1" string="Designer"/>
                            <field name="work_category" readonly="1" string="Work Category"/>
                            <field name="drgs_planned_eta" string="Drgs Planned ETA"
                                   invisible="stage_selection not in ('design')"/>
                            <!-- <field name="prod_rec_date"/> -->
                            <field name="user_id"/>
                            <field name="contractor_id"
                                   invisible="stage_selection in ('design', 'prod_dwngs', 'quality_check', 'installation', 'dispatch')"
                                   required="stage_selection not in ('design', 'prod_dwngs', 'quality_check', 'installation', 'dispatch')"/>
                            <field name="work_status"/>
                            <field name="stage_selection" invisible="1" readonly="1"/>
                            <field name="available_stage_ids" widget="many2many_tags" invisible="1"/>
                            <field name="parent_tracker_id" invisible="not is_revision" readonly="1"/>
                        </group>
                        <group string="Product Information">
                            <field name="item_no" readonly="1"/>
                            <field name="item_no_detail" readonly="1"/>
                            <field name="item_name" readonly="1"/>
                            <field name="area_layout" readonly="1"/>
                            <field name="finish_material" readonly="1" string="Item Finish"/>
                            <field name="finish_material_qty" readonly="1"/>
                            <field name="related_item_name"/>


                            <!-- <<<<<<==================  Design Details ============>>>>>> -->
                            <field name="ppt_update" required="stage_selection in ('design')"
                                   invisible="stage_selection not in ('design')"/>
                            <field name="quote_update" required="stage_selection in ('design')"
                                   invisible="stage_selection not in ('design')"/>
                            <field name="drg_download" required="stage_selection in ('design')"
                                   invisible="stage_selection not in ('design')"/>

                            <!-- <<<<<<==================  Production Drawings Details ============>>>>>> -->
                            <field name="download_date" invisible="stage_selection not in ('prod_dwngs')"/>
                            <field name="msmt_recheck" invisible="stage_selection not in ('prod_dwngs')"
                                   required="stage_selection in ('prod_dwngs')"/>

                            <!-- <<<<<<==================  Production Eng Details ============>>>>>> -->

                            <field name="engg_drgs" invisible="stage_selection not in ('production_eng')"
                                   required="stage_selection in ('production_eng')"/>
                            <field name="bom_jc"
                                   invisible="stage_selection not in ('production_eng', 'carpentry', 'machining', 'metal', 'stone', 'polishing', 'upholstery', 'assembly', 'packaging', 'quality_check')"
                                   required="stage_selection in ('production_eng', 'carpentry', 'machining', 'metal', 'stone', 'polishing', 'upholstery', 'assembly', 'packaging', 'quality_check')"/>
                            <field name="cutting_list" invisible="stage_selection not in ('production_eng')"/>

                            <!-- <<<<<<==================  Carpentry, Machining, MetalStone, Polishing, Upholstery, Assembly, packaging Details ============>>>>>> -->

                            <field name="qc_status"
                                   invisible="stage_selection not in ('carpentry', 'machining', 'pre_assembly', 'metal', 'stone', 'polishing', 'upholstery', 'assembly', 'packaging')"/>
                            <field name="task_assign_date"
                                   invisible="stage_selection not in ('carpentry', 'machining', 'pre_assembly', 'metal', 'stone', 'polishing', 'upholstery', 'assembly', 'packaging')"/>

                            <!-- <<<<<<==================  Quality Check Details ============>>>>>> -->

                            <field name="final_qc_status" invisible="stage_selection not in ('quality_check')"/>

                            <!-- <<<<<<==================  Dispatch Check Details ============>>>>>> -->

                            <field name="dispatch_by" invisible="stage_selection not in ('dispatch')"/>
                            <field name="vehicle_no" invisible="stage_selection not in ('dispatch')"/>
                            <field name="dispatch_date"
                                   invisible="stage_selection not in ('dispatch', 'installation' )"/>

                            <!-- <<<<<<==================  Installation Check Details ============>>>>>> -->

                            <field name="site_arrival_date" invisible="stage_selection not in ('installation')"
                                   required="stage_selection in ('installation')"/>
                            <field name="assigned_to" invisible="stage_selection not in ('installation')"
                                   required="stage_selection in ('installation')"/>
                            <field name="site_qc" invisible="stage_selection not in ('installation')"
                                   required="stage_selection in ('installation')"/>
                            <field name="return_to_factory" invisible="stage_selection not in ('installation')"/>
                            <field name="return_remarks"
                                   invisible="stage_selection not in ('installation') or not return_to_factory"/>


                            <field name="drawing_image_ids" widget="many2many_binary" string="Attachment"
                                   invisible="stage_selection not in ('quality_check')"/>

                            <!--                            <field name="hold_user_ids" required="hold" invisible="not hold" widget="many2many_tags"/>-->

                        </group>
                    </group>
                    <notebook>

                        <page string="Transition Dates">
                            <field name="stage_transition_ids" readonly="1" create="false" edit="false">
                                <list default_order="create_date desc" decoration-danger="days_difference &gt; 0"
                                      decoration-success="days_difference &lt;= 0">
                                    <field name="project_tracker_id"/>
                                    <field name="sale_order_id"/>
                                    <field name="from_stage_id"/>
                                    <field name="to_stage_id"/>
                                    <field name="stage_deadline_date"/>
                                    <field name="work_start_date"/>
                                    <field name="complete_stage_date"/>
                                    <field name="days_difference"/>
                                    <field name="wip_days_difference"/>
                                    <field name="stage_work_status"/>
                                </list>
                            </field>
                        </page>


                        <page string="Boughtout Details">
                            <group>
                                <field name="is_boughtout_req"  widget="boolean_toggle" readonly="stage_selection not in ('design','prod_dwngs')" />
                            </group>
                            <field name="boughtout_ids" invisible="not is_boughtout_req" readonly="stage_selection not in ('design','prod_dwngs','production_eng')" nolabel="">

                                <list default_order="create_date desc" editable="bottom">
                                    <field name="boughtout_id" readonly="qty_add_flag"/>
                                    <field name="scope" readonly="qty_add_flag"/>
                                    <field name="boughtout_selection" readonly="qty_add_flag"/>
                                    <field name="quantity" readonly="not qty_add_flag"/>
                                    <field name="order_date" readonly="qty_add_flag"/>
                                    <field name="receipt_date" readonly="qty_add_flag"/>
                                    <field name="project_tracket_id" column_invisible="1"/>
                                    <field name="qty_add_flag" column_invisible="1"/>
                                </list>
                            </field>
                        </page>


                        <page string="Pre-requisites" name="pre_requisites"
                              invisible="stage_selection not in ('prod_dwngs')">
                            <group>

                                <field name="ppt_final" invisible="stage_selection not in ('prod_dwngs')"
                                       required="stage_selection in ('prod_dwngs')"/>
                                <field name="measurement_reqd" invisible="stage_selection not in ('prod_dwngs')"
                                       required="stage_selection in ('prod_dwngs')"/>
                                <field name="measurement_recd"
                                       invisible="stage_selection not in ('prod_dwngs') or measurement_reqd != 'yes'"
                                       required="stage_selection in ('prod_dwngs')"/>
                            </group>

                        </page>


                        <page string="Stage End Date Info">
                            <group string="End Date Information">
                                <group>
                                    <field name="is_design_stage_available" invisible="1"/>
                                    <field name="is_prod_dwngs_stage_available" invisible="1"/>
                                    <field name="is_production_eng_stage_available" invisible="1"/>
                                    <field name="is_carpentry_stage_available" invisible="1"/>
                                    <field name="is_metal_shop_stage_available" invisible="1"/>
                                    <field name="design_end_date" invisible="not is_design_stage_available"/>
                                    <field name="prod_dwngs_end_date" invisible="not is_prod_dwngs_stage_available"/>
                                    <field name="production_eng_end_date"
                                           invisible="not is_production_eng_stage_available"/>
                                    <field name="carpentry_end_date" invisible="not is_carpentry_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="metal_shop_end_date" invisible="not is_metal_shop_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                </group>

                                <group>
                                    <field name="is_stone_stage_available" invisible="1"/>
                                    <field name="is_polishing_stage_available" invisible="1"/>
                                    <field name="is_upholstry_stage_available" invisible="1"/>
                                    <field name="is_assembly_stage_available" invisible="1"/>
                                    <field name="is_packaging_stage_available" invisible="1"/>
                                    <field name="is_dispatch_stage_available" invisible="1"/>
                                    <field name="is_installation_stage_available" invisible="1"/>

                                    <field name="stone_end_date" invisible="not is_stone_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="polishing_end_date" invisible="not is_polishing_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="upholstry_end_date" invisible="not is_upholstry_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="assembly_end_date" invisible="not is_assembly_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="packaging_end_date" invisible="not is_packaging_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="dispatch_end_date" invisible="not is_dispatch_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                    <field name="installation_end_date" invisible="not is_installation_stage_available"
                                           readonly="stage_selection not in ('production_eng')"/>
                                </group>
                            </group>
                        </page>


                        <page string="Hold Details">

                            <group invisible="stage_selection == 'hold'">
                                <field name="hold" widget="boolean_toggle"/>
                            </group>
                            <group invisible="stage_selection != 'hold'">
                                <field name="un_hold" widget="boolean_toggle"/>
                            </group>

                            <group invisible="stage_selection == 'hold'">
                                <field name="revision_needed" widget="boolean_toggle" invisible="not hold"/>
                                <field name="designer" invisible="not revision_needed"
                                       required="revision_needed == True"
                                       groups="custom_project_tracker.group_project_tracker_designer"/>
                                <field name="pd_hod" invisible="not revision_needed"
                                       required="revision_needed == True"
                                       groups="custom_project_tracker.group_project_tracker_pd_hod"/>
                                <field name="hold_remarks" invisible="not revision_needed" required="revision_needed"
                                       groups="custom_project_tracker.group_project_tracker_designer,custom_project_tracker.group_project_tracker_pd_hod"/>
                                <button name="action_hold" string="Hold Item Confirmed " type="object"
                                        class="btn-primary"
                                        invisible="not revision_needed or hold_requested"
                                        groups="custom_project_tracker.group_project_tracker_designer,custom_project_tracker.group_project_tracker_pd_hod"/>
                            </group>

                            <group invisible="stage_selection != 'hold'">
                                <field name="revision_done" widget="boolean_toggle" invisible="not un_hold"/>
                                <field name="unhold_remarks" required="revision_done" invisible="not revision_done"/>
                                <button name="open_revision_wizard" type="object" string="Un Hold"
                                        class="btn-primary" invisible="not revision_done"
                                        groups="custom_project_tracker.group_create_revision"/>
                                <field name="is_revision" invisible="1"/>
                            </group>

                            <group string="Project Hold Information">
                                <field name="hold_start_date" readonly="1"/>
                                <field name="hold_end_date" readonly="1"/>
                                <field name="total_hold_duration" readonly="1"/>
                            </group>
                        </page>


                        <page string="Revisions" invisible="not has_revisions">
                            <field name="has_revisions" readonly="1" invisible="1"/>
                            <field name="revision_ids" readonly="1">
                                <list editable="top">
                                    <field name="customer_id"/>
                                    <field name="quotation_id"/>
                                    <field name="item_no"/>
                                    <field name="item_name"/>
                                    <field name="qty"/>
                                    <field name="planned_dispatch"/>
                                    <field name="name" string="Revision Reason"/>
                                    <field name="date" string="Revision Date"/>
                                </list>
                            </field>
                        </page>

                        <page string="Remarks" name="remarks">
                            <field name="remarks" nolabel="1"/>
                        </page>

                    </notebook>
                </sheet>
                <chatter reload_on_post="True"/>
            </form>
        </field>
    </record>

    <record id="view_project_tracker_tree" model="ir.ui.view">
        <field name="name">project.tracker.tree</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <list string="Project Tracker" expand="1">
                <field name="customer_id" optional='show'/>
                <field name="sale_order_id" optional='show'/>
                <field name="item_no" optional='show'/>
                <field name="item_name" optional='show'/>
                <field name="area_layout" optional='show'/>
                <field name="finish_material" optional='show'/>
                <field name="finish_material_qty" optional='show'/>
                <field name="work_status" optional='show'/>
                <field name="contractor_id" optional='show'/>
                <field name="date" optional='show'/>
                <field name="remarks" optional='show'/>
            </list>
        </field>
    </record>

    <record id="view_project_tracker_search" model="ir.ui.view">
        <field name="name">project.tracker.search</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <search string="Search Project Tracker">
                <field name="customer_id"/>
                <field name="sale_order_id"/>
                <field name="item_no"/>
                <field name="item_name"/>
                <field name="stage_id"/>
                <field name="contractor_id"/>
                <filter name="filter_active" string="Archive" domain="[('active', '=', False)]"/>
                <group expand="1" string="Group By">
                    <filter name="group_customer" string="Customer" context="{'group_by': 'customer_id'}"/>
                    <filter name="group_sale_order" string="Quote No" context="{'group_by': 'sale_order_id'}"/>
                    <filter name="group_prod_date" string="Prod Rec Date" context="{'group_by': 'prod_rec_date'}"/>
                    <filter name="group_item" string="Item Name" context="{'group_by': 'item_name'}"/>
                    <filter name="group_item_no " string="Item No." context="{'group_by': 'item_no'}"/>
                    <filter name="group_contractor" string="Contractor" context="{'group_by': 'contractor_id'}"/>
                    <filter name="group_status" string="Status" context="{'group_by': 'stage_id'}"/>
                    <filter name="group_date" string="Date" context="{'group_by': 'date'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="view_project_tracker_pivot" model="ir.ui.view">
        <field name="name">project.tracker.pivot</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <pivot string="Project Tracker Analysis">
                <field name="customer_id" type="row"/>
                <field name="stage_id" type="col"/>
                <field name="finish_material_qty" type="measure"/>
            </pivot>
        </field>
    </record>

    <record id="view_project_tracker_kanban" model="ir.ui.view">
        <field name="name">project.tracker.kanban</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <kanban default_group_by="stage_id" class="o_kanban_small_column o_opportunity_kanban">
                <field name="customer_id"/>
                <field name="item_name"/>
                <field name="item_no"/>
                <field name="stage_id"/>
                <field name="date"/>
                <field name="sale_order_id"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_global_click p-2 rounded-3 position-relative shadow-sm bg-white h-100">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <field name="customer_id" class="fw-bold fs-5 text-primary text-truncate me-2"/>
                                <field name="sale_order_id" class="badge rounded-pill bg-primary text-white px-2 py-1"/>
                            </div>
                            <div class="mb-2 p-2 rounded-3 bg-light">
                                <div>
                                    <span class="fw-semibold text-dark">Item:</span>
                                    <span class="text-dark">
                                        <field name="item_name" class="fw-medium"/>
                                    </span>
                                </div>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="text-nowrap">
                                    <span class="badge bg-secondary text-white me-1">Item No.</span>
                                    <field name="item_no" class="fw-bold text-dark"/>
                                </div>
                                <div class="text-nowrap">
                                    <field name="date" class="badge bg-warning text-dark px-2 py-1 rounded-pill"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>


    <record id="view_project_tracker_gantt" model="ir.ui.view">
        <field name="name">project.tracker.gantt</field>
        <field name="model">project.tracker</field>
        <field name="arch" type="xml">
            <gantt date_start="date" date_stop="prod_rec_date" string="Project Timeline">
                <field name="item_name"/>
            </gantt>
        </field>
    </record>

    <record id="action_project_tracker" model="ir.actions.act_window">
        <field name="name">Project Tracker</field>
        <field name="res_model">project.tracker</field>
        <field name="view_mode">list,kanban,form,pivot,gantt</field>
        <field name="context">{'group_by': ['customer_id', 'sale_order_id']}</field>
    </record>

    <menuitem id="menu_project_tracker_root" name="Project Tracker"
              web_icon="custom_project_tracker,static/description/icon.png"/>

    <menuitem id="menu_project_tracker_configuration" name="Configuration" parent="menu_project_tracker_root"
              sequence="30"/>


    <menuitem id="menu_project_tracker_main" name="Items" parent="menu_project_tracker_root" sequence="5"/>
    <menuitem id="menu_project_tracker_action" name="Track Items" parent="menu_project_tracker_main"
              action="action_project_tracker" sequence="10"/>

    <menuitem id="menu_project_tracker_quotation" name="Quotations" parent="menu_project_tracker_root"
              action="sale.action_quotations" sequence="1"/>
</odoo>
