<odoo>
    <data noupdate="1">

        <record id="boughtout_product_fabric" model="product.template">
            <field name="name">Fabric</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_leather" model="product.template">
            <field name="name">Leather</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_wallpaper" model="product.template">
            <field name="name">Wallpaper</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_stone" model="product.template">
            <field name="name">Stone</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_glass" model="product.template">
            <field name="name">Glass</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_mirror" model="product.template">
            <field name="name">Mirror</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_spcl_hardware" model="product.template">
            <field name="name">Spcl Hardware</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_knobs" model="product.template">
            <field name="name">Knobs</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_handles" model="product.template">
            <field name="name">Handles</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_decor_lights" model="product.template">
            <field name="name">Decor Lights</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_appliance" model="product.template">
            <field name="name">Appliance</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_sink_faucet" model="product.template">
            <field name="name">Sink/ Faucet</field>
            <field name="is_boughtout">True</field>
        </record>

        <record id="boughtout_product_heating_pad" model="product.template">
            <field name="name">Heating Pad</field>
            <field name="is_boughtout">True</field>
        </record>

    </data>
</odoo>
