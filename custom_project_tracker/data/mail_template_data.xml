<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- <PERSON>ail Template for New Quote Created -->
        <record id="email_template_new_quote_created" model="mail.template">
            <field name="name">New Quote Created Notification</field>
            <field name="model_id" ref="sale.model_sale_order"/>
            <field name="subject">New Quote Created - ${object.name} - ${object.partner_id.name}</field>
            <field name="email_from">${(object.user_id.email or object.company_id.email or 'noreply@localhost')|safe}</field>
            <field name="email_to">${object.partner_id.email}</field>
            <field name="body_html" type="html">
<div style="font-family: Arial, sans-serif; font-size: 14px; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background-color: #2E86AB; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0;">
        <h2 style="margin: 0; font-size: 24px;">New Quote Created</h2>
    </div>

    <div style="background-color: #f8f9fa; padding: 20px; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px;">
        <p style="margin: 0 0 20px 0; font-size: 16px;">
            <strong>Dear Team,</strong>
        </p>

        <p style="margin: 0 0 20px 0;">
            A new quote has been created in the system. Please find the details below:
        </p>

        <table style="width: 100%; border-collapse: collapse; margin: 20px 0; background-color: white;">
            <tr style="background-color: #e9ecef;">
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold; width: 40%;">Quote Number:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.name}</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Customer:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.partner_id.name}</td>
            </tr>
            <tr style="background-color: #e9ecef;">
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Quote Date:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.date_order.strftime('%d/%m/%Y') if object.date_order else 'N/A'}</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Designer:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.designer_id.name if object.designer_id else 'N/A'}</td>
            </tr>
            <tr style="background-color: #e9ecef;">
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Work Category:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${dict(object._fields['work_category'].selection).get(object.work_category, 'N/A') if object.work_category else 'N/A'}</td>
            </tr>
            <tr>
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Planned Dispatch:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.planned_dispatch.strftime('%d/%m/%Y') if object.planned_dispatch else 'N/A'}</td>
            </tr>
            <tr style="background-color: #e9ecef;">
                <td style="padding: 12px; border: 1px solid #ddd; font-weight: bold;">Total Amount:</td>
                <td style="padding: 12px; border: 1px solid #ddd;">${object.amount_total} ${object.currency_id.name if object.currency_id else ''}</td>
            </tr>
        </table>

        <p style="margin: 20px 0;">
            Please review the quote and take necessary actions as per your role.
        </p>

        <div style="text-align: center; margin: 30px 0;">
            <a href="/web#id=${object.id}&amp;model=sale.order&amp;view_type=form"
               style="background-color: #2E86AB; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; font-weight: bold; display: inline-block;">
                View Quote in System
            </a>
        </div>

        <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;"/>

        <p style="margin: 0; font-size: 12px; color: #666; text-align: center;">
            This is an automated notification from the Project Tracker System.<br/>
            <strong>${object.company_id.name}</strong>
            % if object.company_id.phone:
                | Phone: ${object.company_id.phone}
            % endif
            % if object.company_id.email:
                | Email: ${object.company_id.email}
            % endif
        </p>
    </div>
</div>
            </field>
            <field name="auto_delete" eval="True"/>
            <field name="lang">${object.partner_id.lang}</field>
        </record>
    </data>
</odoo>
