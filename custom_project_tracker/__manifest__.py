{
    'name': 'Custom Project Tracker',
    'version': '18.1.0',
    'category': 'Project',
    'summary': 'Track project items with detailed status',
    'depends': ['base', 'web', 'project', 'mail', 'sale', 'sale_management', 
                'base_vat', 'calendar', 'crm', 'account', 'stock', 'purchase',
                'purchase_stock', 'sale_pdf_quote_builder'],
    'data': [
        'security/res_group.xml',
        'security/ir.model.access.csv',
        
        'data/boughtout_data.xml',
        'data/stage_demo_data.xml',
        'data/check_deadline_action.xml',

        'views/project_tracker_views.xml',
        'views/stage_view.xml',
        'views/boughtout_view.xml',
        'views/sale_order_view.xml',
        'views/product_template_view.xml',
        'views/mail_activity_view.xml',
        'views/stage_transition_view.xml',
        'views/res_partner.xml',
        'views/res_users_view.xml',
        'wizard/project_tracker_revision_view.xml',
        'wizard/stage_selection_wizard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'custom_project_tracker/static/src/js/qcent_many2many_attachment_preview.js',
            'custom_project_tracker/static/src/xml/activity_template_inherit.xml',
            'custom_project_tracker/static/src/xml/qcent_many2many_attachment_preview_template.xml',
        ],
    },
    'installable': True,
    'application': True,
}
