p, span, a, ul, li, button {
    font-size: inherit;
	font-weight: inherit;
	line-height: inherit;
}

strong {
	font-weight: 600;
}

.head {
	line-height: 1.5em;
	font-weight: 300;
}

strong {
  font-weight: 400;
}

.sub_title {
	font-size: 14px;
}

.sub_title div span {
	font-weight: 600;
}

.chart #canvas_graph {
	height: 400px !important;
}

.highcharts-background {
	fill: none;
}
.text-mauve{
    padding-right: 10px;
    font-size: 30px;
    padding-left: 0px;
    padding-bottom: 5px;
    padding-top: 10px;
}

.dashboard-card__detail {
   flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding-left: 0px;
    padding-bottom: 5px;
    padding-top: 10px;
    margin:15px
}

/* Apply common styles to all tables */
.table_activity {
    width: 100%;
    padding: 24px;
    background-color: white;
    color: #454748;
    border-collapse: separate;
    margin-top: 20px; /* Adjust the margin-top value as needed */
}

/* Apply specific styles to the table headers */
.table_activity thead {
    background-color: #f5f5f5; /* Add a background color to the header */
}

/* Apply styles to the table head cells */
.table_activity th {
    text-align: left; /* Adjust text alignment as needed */
}

.activity_head{
font-weight: bold;
font-size: 20px;
}

.click-view{
border-radius: 8px;
background-color: #c4c4f2;
width: 100px;height: 35px;
}
    # },

.click-origin-view{
border-radius: 8px;
background-color: #c4c4f2;
width: 100px;
height: 35px;
}
