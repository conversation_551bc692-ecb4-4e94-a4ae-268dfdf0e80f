.o_action_manager {
  direction: ltr;
  webkit-box-flex: 1;
  webkit-flex: 1 1 auto;
  flex: 1 1 auto;
  overflow: scroll !important;
}
.activity-dashboard-card {
    display: flex;
    flex-direction: row; /* Change to row for linear arrangement */
    justify-content: center;
    border-radius: 0.3rem;
    padding: 1.7rem 1.5rem 1.5rem 1.5rem;
    margin: 1rem auto;
    height: 100px;
    cursor: pointer;
    align-items: center;
    box-sizing: border-box;
    border-radius: 10px;
    font-size: 35px;
}


.activity-dashboard-card__icon-container {
    height: 50px;
    border-radius: 50%;
}
.my_activity {
  background-color: #229db9;
}
.all_activity{
    background: #88dfdf;
}
.planned_activity {
  background-color: #e8c5ac;
}
.completed_activity{
    background: #a7a1f4;
}

.today_activity{
    background: #f3bdf4;
}
.overdue_activity {
  background-color: #a2d2fb;
}
.cancelled_activity{
    background: #beeecc;
}
.activity_type {
  background-color: #ecb9b9;
}
.dashboard_main_section {
  margin: 20px;
}